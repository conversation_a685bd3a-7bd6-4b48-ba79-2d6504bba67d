<?php
require("../database.php");

// Check admin session
if (isset($_SESSION['profile_email'])) {
    $admin_name   = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    if ($admin_roleid != 'gPHOfKV0sL') {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        exit;
    }
} else {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Get POST data
$search_term = isset($_POST['search_term']) ? trim($_POST['search_term']) : '';
$event_mgm_id = isset($_POST['event_mgm_id']) ? $_POST['event_mgm_id'] : '';
$categories = isset($_POST['categories']) ? $_POST['categories'] : [];

if (empty($search_term) || empty($event_mgm_id)) {
    echo json_encode(['error' => 'Missing required parameters']);
    exit;
}

// Search for player across all categories
$search_query = "
    SELECT DISTINCT 
        rf.fullname,
        rf.form_id,
        em.category_id,
        ec.category_name
    FROM registration_form rf
    JOIN scorecard s ON rf.form_id = s.form_id
    JOIN event_mgm em ON s.event_id = em.event_id
    JOIN event_category ec ON em.category_id = ec.category_id
    WHERE em.event_mgm_id = ?
    AND LOWER(rf.fullname) LIKE LOWER(?)
    ORDER BY rf.fullname ASC
    LIMIT 1
";

$search_stmt = $conn->prepare($search_query);
$search_like = '%' . $search_term . '%';
$search_stmt->bind_param("ss", $event_mgm_id, $search_like);
$search_stmt->execute();
$search_result = $search_stmt->get_result();

if ($search_result->num_rows > 0) {
    $player = $search_result->fetch_assoc();
    echo json_encode([
        'found' => true,
        'player_name' => $player['fullname'],
        'form_id' => $player['form_id'],
        'category_id' => $player['category_id'],
        'category_name' => $player['category_name']
    ]);
} else {
    echo json_encode([
        'found' => false,
        'message' => 'No player found with that name'
    ]);
}

$search_stmt->close();
$conn->close();
?>
