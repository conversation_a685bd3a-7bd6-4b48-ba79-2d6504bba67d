<?php
require("database.php");

// Get the event_mgm_id and category_id from the query string
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
$category_id = isset($_GET['category_id']) ? $_GET['category_id'] : '';

// Fetch mode (GROSS/NETT/SYSTEM36/STABLEFORD) early
$mode = 'GROSS'; // Default mode
$mode_query = "SELECT mode FROM mode_of_game WHERE event_mgm_id = ? LIMIT 1";
$mode_stmt = $conn->prepare($mode_query);
$mode_stmt->bind_param("s", $event_mgm_id);
$mode_stmt->execute();
$mode_result = $mode_stmt->get_result();
if ($mode_row = $mode_result->fetch_assoc()) {
    $mode = $mode_row['mode'];
}
$mode_stmt->close();

// Initialize max_score and max_net - only needed for NETT mode
$max_score = 0;
$max_net = 0;
if ($mode === 'NETT') {
    $nett_query = "SELECT max_score, max_net FROM nett_format WHERE event_mgm_id = ? LIMIT 1";
    $nett_stmt = $conn->prepare($nett_query);
    $nett_stmt->bind_param("s", $event_mgm_id);
    $nett_stmt->execute();
    $nett_result = $nett_stmt->get_result();
    if ($nett_row = $nett_result->fetch_assoc()) {
        $max_score = (int)$nett_row['max_score'];
        $max_net = (int)$nett_row['max_net'];
    }
    $nett_stmt->close();
}

// Fetch event details
$event_query = "
    SELECT event_mgm.event_id, event_mgm.event_name, club_info.golf_club_name AS event_venue, event_mgm.event_start_date, event_mgm.event_end_date
        FROM event_mgm
        JOIN club_info ON event_mgm.event_venue = club_info.club_id
        WHERE event_mgm.event_mgm_id = ?
";
$event_stmt = $conn->prepare($event_query);
$event_stmt->bind_param("s", $event_mgm_id);
$event_stmt->execute();
$event_result = $event_stmt->get_result();
$event_details = $event_result->fetch_assoc();

// Fetch logos for the event from live_logo table
$logos = [];
$logos_query = "
    SELECT logo 
    FROM live_logo 
    WHERE event_mgm_id = ?
";
$logos_stmt = $conn->prepare($logos_query);
$logos_stmt->bind_param("s", $event_mgm_id);
$logos_stmt->execute();
$logos_result = $logos_stmt->get_result();
while ($logo_row = $logos_result->fetch_assoc()) {
    if (!empty($logo_row['logo'])) {
        $logos[] = $logo_row['logo'];
    }
}
$logos_stmt->close();

// Fetch categories for the event
$category_query = "
    SELECT DISTINCT ec.category_id, ec.category_name
    FROM event_mgm em
    JOIN event_category ec ON em.category_id = ec.category_id
    WHERE em.event_mgm_id = ?
";
$category_stmt = $conn->prepare($category_query);
$category_stmt->bind_param("s", $event_mgm_id);
$category_stmt->execute();
$category_result = $category_stmt->get_result();
$categories = [];
while ($row = $category_result->fetch_assoc()) {
    $categories[] = $row;
}

$category_id = isset($_GET['category_id']) ? $_GET['category_id'] : (count($categories) > 0 ? $categories[0]['category_id'] : '');

// Fetch the event_id based on event_mgm_id and category_id
$event_id_query = "
    SELECT event_id
    FROM event_mgm
    WHERE event_mgm_id = ? AND category_id = ?
";
$event_id_stmt = $conn->prepare($event_id_query);
$event_id_stmt->bind_param("ss", $event_mgm_id, $category_id);
$event_id_stmt->execute();
$event_id_result = $event_id_stmt->get_result();
$event_id_row = $event_id_result->fetch_assoc();
$event_id = $event_id_row['event_id'];

// Fetch distinct rounds for the event
$rounds_query = "
    SELECT DISTINCT s.round
    FROM scorecard s
    JOIN event_mgm em ON s.event_id = em.event_id
    WHERE em.event_mgm_id = ? AND em.category_id = ?
    ORDER BY s.round
";
$rounds_stmt = $conn->prepare($rounds_query);
$rounds_stmt->bind_param("ss", $event_mgm_id, $category_id);
$rounds_stmt->execute();
$rounds_result = $rounds_stmt->get_result();
$rounds = [];
while ($row = $rounds_result->fetch_assoc()) {
    $rounds[] = $row['round'];
}

// ---------------------------------------------------------------------
// Build scoreboard using hole-by-hole logic like result.php
// ---------------------------------------------------------------------
$player_scores = [];
$latest_round  = !empty($rounds) ? max($rounds) : 1;

if ($mode === 'GROSS') {
    // GROSS mode aggregated query
$scores_query = "
        SELECT s.form_id, p.fullname, p.nationality, IFNULL(p.handicap, 0) AS handicap, p.hide, p.hide_rmk,
               s.round, SUM(IF(s.strokes > 0, s.strokes, 0)) AS total_strokes, 
               SUM(IF(s.strokes > 0, s.strokes - h.par, 0)) AS score,
               COALESCE(MAX(s.ocb), 0) as ocb
    FROM scorecard s
    JOIN registration_form p ON s.form_id = p.form_id
    JOIN custom_hole h ON s.custom_hole_id = h.custom_hole_id
    JOIN event_mgm em ON s.event_id = em.event_id
    WHERE em.event_mgm_id = ? AND em.category_id = ?
    GROUP BY s.form_id, s.round
    ORDER BY score ASC
";
$scores_stmt = $conn->prepare($scores_query);
$scores_stmt->bind_param("ss", $event_mgm_id, $category_id);
$scores_stmt->execute();
$scores_result = $scores_stmt->get_result();

while ($row = $scores_result->fetch_assoc()) {
    $player_id = $row['form_id'];
        $rnd = $row['round'];
        
        if (!isset($player_scores[$player_id])) {
            $player_scores[$player_id] = [
                'id' => $player_id,
                'name' => $row['fullname'],
                'country' => $row['nationality'],
                'handicap' => (int)($row['handicap'] ?? 0),
                'hide' => $row['hide'],
                'hide_rmk' => $row['hide_rmk'],
                'total_strokes' => 0,
                'score' => 0,
                'rounds' => [],
                'ocb' => 0,
                'hole_display' => '-'
            ];
        }
        
        if (!isset($player_scores[$player_id]['rounds'][$rnd])) {
            $player_scores[$player_id]['rounds'][$rnd] = 0;
        }
        $player_scores[$player_id]['rounds'][$rnd] += (int)$row['total_strokes'];
        $player_scores[$player_id]['total_strokes'] += (int)$row['total_strokes'];
        $player_scores[$player_id]['score'] += (int)$row['score'];
        if ($rnd == $latest_round) {
            $player_scores[$player_id]['ocb'] = $row['ocb'];
        }
    }
    $scores_stmt->close();
    
} else if ($mode === 'NETT' || $mode === 'SYSTEM36' || $mode === 'STABLEFORD') {
    // NETT and SYSTEM36 mode: hole-by-hole calculation
    $holesSql = "
        SELECT s.form_id, p.fullname, p.nationality, IFNULL(p.handicap, 0) AS handicap, p.hide, p.hide_rmk,
               s.round, s.strokes, h.par, h.hole_number, h.golf_index, COALESCE(s.ocb, 0) as ocb
        FROM scorecard s
        JOIN registration_form p ON s.form_id = p.form_id
        JOIN custom_hole h ON s.custom_hole_id = h.custom_hole_id
        JOIN event_mgm em ON s.event_id = em.event_id
        WHERE em.event_mgm_id = ? AND em.category_id = ?
        ORDER BY s.form_id, s.round, h.hole_number
    ";
    $hStmt = $conn->prepare($holesSql);
    $hStmt->bind_param("ss", $event_mgm_id, $category_id);
    $hStmt->execute();
    $hRes = $hStmt->get_result();

    $rawData = [];
    while ($row = $hRes->fetch_assoc()) {
        $fid = $row['form_id'];
        $roundNo = (int)$row['round'];
        if (!isset($rawData[$fid])) {
            $rawData[$fid] = [
                'fullname' => $row['fullname'],
                'nationality' => $row['nationality'],
                'handicap' => (int)$row['handicap'],
                'hide' => (int)$row['hide'],
                'hide_rmk' => (int)$row['hide_rmk'],
                'rounds' => []
            ];
        }
        if (!isset($rawData[$fid]['rounds'][$roundNo])) {
            $rawData[$fid]['rounds'][$roundNo] = [];
        }
        $rawData[$fid]['rounds'][$roundNo][] = [
            'strokes' => (int)$row['strokes'],
            'par' => (int)$row['par'],
            'hole_number' => (int)$row['hole_number'],
            'golf_index' => (int)$row['golf_index'],
            'ocb' => (int)$row['ocb']
        ];
    }
    $hStmt->close();

    // Process the grouped data
    foreach ($rawData as $fid => $pObj) {
        if (!isset($player_scores[$fid])) {
            $player_scores[$fid] = [
                'id' => $fid,
                'name' => $pObj['fullname'],
                'country' => $pObj['nationality'],
                'handicap' => $pObj['handicap'],
                'hide' => $pObj['hide'],
                'hide_rmk' => $pObj['hide_rmk'],
                'total_strokes' => 0,
                'score' => 0,
                'nett' => 0,
                'system36_points' => 0,
                'stableford_points' => 0,
                'hcp_of_day' => 0,
                'rounds' => [],
                'rounds_nett' => [],
                'rounds_points' => [],
                'ocb' => 0,
                'hole_display' => '-'
            ];
        }
        
        foreach ($pObj['rounds'] as $rNo => $holesArr) {
            $roundStrokes = 0;
            $roundPar = 0;
            $roundPoints = 0;
            $roundSystem36Points = 0; // For STABLEFORD HCP of Day calculation
            $holesPlayed = 0;
            $round_ocb_sum = 0;
            $round_ocb_count = 0;

            foreach ($holesArr as $hInfo) {
                $st = (int)$hInfo['strokes'];
                $par = (int)$hInfo['par'];
                $holeNum = (int)$hInfo['hole_number'];
                $golfIndex = (int)$hInfo['golf_index'];
                $ocbV = (int)$hInfo['ocb'];

                if ($st > 0) {
                    // Apply maximum stroke cap if max_score > 0 (only for NETT mode)
                    if ($mode === 'NETT' && isset($max_score) && $max_score > 0) {
                        $cap = $par + $max_score;
                        if ($st > $cap) {
                            $st = $cap;
                        }
                    }
                    
                    if ($mode === 'SYSTEM36') {
                        // Calculate System 36 points: Result - Par
                        $nettScore = $st - $par;
                        $points = getSystem36Points($nettScore);
                        $roundPoints += $points;
                    }
                    
                    if ($mode === 'STABLEFORD') {
                        // For STABLEFORD, first calculate System 36 points for HCP of Day calculation
                        $system36NettScore = $st - $par; // Result - Par (no handicap)
                        $system36Points = getSystem36Points($system36NettScore);
                        $roundSystem36Points += $system36Points;
                        
                        // Note: Stableford points will be calculated AFTER we know the HCP of Day
                        // For now, just accumulate System 36 points
                    }
                    
                    $holesPlayed++;
                    $roundStrokes += $st;
                    $roundPar += $par;
                    $round_ocb_sum += $ocbV;
                    $round_ocb_count++;
                }
            }

            $round_ocb = $round_ocb_count > 0 ? $round_ocb_sum / $round_ocb_count : 0;

            $player_scores[$fid]['rounds'][$rNo] = $roundStrokes;
            $player_scores[$fid]['total_strokes'] += $roundStrokes;
            $player_scores[$fid]['score'] += ($roundStrokes - $roundPar);
            
            if ($mode === 'NETT') {
                $raw_round_nett = $roundStrokes - $player_scores[$fid]['handicap'];
                $min_round_nett = isset($max_net) ? $roundPar - $max_net : $raw_round_nett;
                $round_nett = ($raw_round_nett < $min_round_nett) ? $min_round_nett : $raw_round_nett;
                $player_scores[$fid]['rounds_nett'][$rNo] = $round_nett;
                $player_scores[$fid]['nett'] += $round_nett;
            } else if ($mode === 'SYSTEM36') {
                $player_scores[$fid]['rounds_points'][$rNo] = $roundPoints;
                $player_scores[$fid]['system36_points'] += $roundPoints;
            } else if ($mode === 'STABLEFORD') {
                // Don't set stableford_points in first pass - will be calculated in second pass
                $player_scores[$fid]['system36_points'] += $roundSystem36Points; // Add System 36 points for HCP calculation
            }
            
            $player_scores[$fid]['ocb'] = $round_ocb;

            if ($rNo == $latest_round) {
                $player_scores[$fid]['hole_display'] = ($holesPlayed >= 18) ? 'F' : ($holesPlayed > 0 ? $holesPlayed : '-');
            }
        }
        
        // Calculate HCP of the day for System 36 and Stableford
        if ($mode === 'SYSTEM36' || $mode === 'STABLEFORD') {
            // Count actual holes played (with strokes > 0)
            $totalHolesPlayed = 0;
            foreach ($pObj['rounds'] as $rNo => $holesArr) {
                foreach ($holesArr as $hInfo) {
                    $st = (int)$hInfo['strokes'];
                    if ($st > 0) {
                        $totalHolesPlayed++;
                    }
                }
            }
            
            // Calculate max possible points based on actual holes played (2 points per hole)
            $maxPossiblePoints = $totalHolesPlayed * 2;
            $player_scores[$fid]['hcp_of_day'] = $maxPossiblePoints - $player_scores[$fid]['system36_points'];
            

            

        }
    }

    // Note: HCP of Day for STABLEFORD mode is calculated from System 36 points below
    // Do not override it with player handicap
}

// Ensure all players in STABLEFORD mode have correct hcp_of_day (using System 36 points)
if ($mode === 'STABLEFORD') {
    foreach ($player_scores as $fid => &$pData) {
        if (isset($pData['system36_points']) && (!isset($pData['hcp_of_day']) || $pData['hcp_of_day'] == 0)) {
            // Calculate actual holes played for this player
            $totalHoles = 0;
            if (isset($rawData[$fid]['rounds'])) {
                foreach ($rawData[$fid]['rounds'] as $rNo => $holesArr) {
                    $totalHoles += count($holesArr);
                }
            }
            
            if ($totalHoles > 0) {
                $maxPossiblePoints = $totalHoles * 2; // 2 points per hole maximum
                $oldHcpOfDay = $pData['hcp_of_day'];
                $pData['hcp_of_day'] = $maxPossiblePoints - $pData['system36_points'];
                

            }
        }
    }
    unset($pData);
    
    // Second pass: Now recalculate Stableford points using HCP of Day for handicap allocation
    foreach ($rawData as $fid => $pObj) {
        if (!isset($player_scores[$fid])) continue;
        
        $hcp_of_day = $player_scores[$fid]['hcp_of_day'];
        $total_stableford_points = 0;
        $rounds_points = [];
        
        // Calculate total holes for debug output
        $totalHoles = 0;
        foreach ($pObj['rounds'] as $rNo => $holesArr) {
            $totalHoles += count($holesArr);
        }
        
        foreach ($pObj['rounds'] as $rNo => $holesArr) {
            $roundPoints = 0;
            
            foreach ($holesArr as $hInfo) {
                $st = (int)$hInfo['strokes'];
                $par = (int)$hInfo['par'];
                $golfIndex = (int)$hInfo['golf_index'];
                
                if ($st > 0) {
                    // Calculate handicap strokes using HCP of Day
                    $handicapStrokes = 0;
                    if ($golfIndex <= $hcp_of_day) {
                        $handicapStrokes = 1;
                        // If HCP of Day > 18, player gets additional strokes on hardest holes
                        if ($hcp_of_day > 18 && $golfIndex <= ($hcp_of_day - 18)) {
                            $handicapStrokes = 2;
                        }
                        // If HCP of Day > 36, player gets third stroke on hardest holes  
                        if ($hcp_of_day > 36 && $golfIndex <= ($hcp_of_day - 36)) {
                            $handicapStrokes = 3;
                        }
                    }
                    
                    // Calculate net score: strokes - par - handicap strokes
                    $nettScore = $st - $par - $handicapStrokes;
                    $points = getStablefordPoints($nettScore);
                    $roundPoints += $points;
                }
            }
            
            $rounds_points[$rNo] = $roundPoints;
            $total_stableford_points += $roundPoints;
        }
        
        // Update player data with correct Stableford points
        $player_scores[$fid]['stableford_points'] = $total_stableford_points;
        $player_scores[$fid]['rounds_points'] = $rounds_points;
        

    }
}

// Set hole display for players who haven't been processed yet
foreach ($player_scores as $fid => &$pScore) {
    if ($pScore['hole_display'] === '-') {
        // Fetch the latest hole played
    $latest_hole_query = "
        SELECT h.hole_number, s.strokes
        FROM scorecard s
        JOIN custom_hole h ON s.custom_hole_id = h.custom_hole_id
        WHERE s.form_id = ? AND s.round = ?
        ORDER BY s.custom_hole_id DESC
        LIMIT 1
    ";
    $latest_hole_stmt = $conn->prepare($latest_hole_query);
        $latest_hole_stmt->bind_param("si", $fid, $latest_round);
    $latest_hole_stmt->execute();
    $latest_hole_result = $latest_hole_stmt->get_result();
    $latest_hole_row = $latest_hole_result->fetch_assoc();

        if ($latest_hole_row && $latest_hole_row['strokes'] >= 0) {
            $pScore['hole_display'] = $latest_hole_row['hole_number'];
        }
        $latest_hole_stmt->close();
    }
}

// Convert to array for further processing
$player_scores = array_values($player_scores);

// ---------------------------------------------------------------------
// Sort player scores based on mode
// ---------------------------------------------------------------------
function isNoScore($player) {
    return !isset($player['total_strokes']) || $player['total_strokes'] === null || $player['total_strokes'] === '' || $player['total_strokes'] == 0;
}

usort($player_scores, function($a, $b) use ($mode, $conn, $event_mgm_id) {
    if (isNoScore($a) && isNoScore($b)) return 0;
    if (isNoScore($a)) return 1;
    if (isNoScore($b)) return -1;

    if ($mode === 'NETT') {
        // compare nett scores
        if ($a['nett'] === $b['nett']) {
            // Check if OCB is enabled for this event
            $check_ocb = "SELECT apply_ocb FROM ocb_rules_apply WHERE event_mgm_id = ? AND apply_ocb = 1";
            $ocb_stmt = $conn->prepare($check_ocb);
            $ocb_stmt->bind_param("s", $event_mgm_id);
            $ocb_stmt->execute();
            $ocb_result = $ocb_stmt->get_result();

            if ($ocb_result && $ocb_result->num_rows > 0) {
                return ($b['ocb'] <=> $a['ocb']); // Higher OCB wins
            } else {
                return ($a['total_strokes'] <=> $b['total_strokes']); // Lower total strokes wins
            }
        }
        return ($a['nett'] < $b['nett']) ? -1 : 1;
    } else if ($mode === 'SYSTEM36') {
        // compare HCP of the day (lower HCP is better)
        if ($a['hcp_of_day'] === $b['hcp_of_day']) {
            // Check if OCB is enabled for this event
            $check_ocb = "SELECT apply_ocb FROM ocb_rules_apply WHERE event_mgm_id = ? AND apply_ocb = 1";
            $ocb_stmt = $conn->prepare($check_ocb);
            $ocb_stmt->bind_param("s", $event_mgm_id);
            $ocb_stmt->execute();
            $ocb_result = $ocb_stmt->get_result();

            if ($ocb_result && $ocb_result->num_rows > 0) {
                return ($b['ocb'] <=> $a['ocb']); // Higher OCB wins
            } else {
                return ($a['total_strokes'] <=> $b['total_strokes']); // Lower total strokes wins
            }
        }
        return ($a['hcp_of_day'] < $b['hcp_of_day']) ? -1 : 1;
    } else if ($mode === 'STABLEFORD') {
        // compare Stableford points (higher points are better)
        if ($a['stableford_points'] === $b['stableford_points']) {
            // Check if OCB is enabled for this event
            $check_ocb = "SELECT apply_ocb FROM ocb_rules_apply WHERE event_mgm_id = ? AND apply_ocb = 1";
            $ocb_stmt = $conn->prepare($check_ocb);
            $ocb_stmt->bind_param("s", $event_mgm_id);
            $ocb_stmt->execute();
            $ocb_result = $ocb_stmt->get_result();

            if ($ocb_result && $ocb_result->num_rows > 0) {
                return ($b['ocb'] <=> $a['ocb']); // Higher OCB wins
            } else {
                return ($a['total_strokes'] <=> $b['total_strokes']); // Lower total strokes wins
            }
        }
        return ($a['stableford_points'] > $b['stableford_points']) ? -1 : 1;
    } else {
        // GROSS mode
        // compare gross scores
        if ($a['score'] === $b['score'] && $a['total_strokes'] === $b['total_strokes']) {
            // Check if OCB is enabled for this event
            $check_ocb = "SELECT apply_ocb FROM ocb_rules_apply WHERE event_mgm_id = ? AND apply_ocb = 1";
            $ocb_stmt = $conn->prepare($check_ocb);
            $ocb_stmt->bind_param("s", $event_mgm_id);
            $ocb_stmt->execute();
            $ocb_result = $ocb_stmt->get_result();

            if ($ocb_result && $ocb_result->num_rows > 0) {
                return ($b['ocb'] <=> $a['ocb']); // Higher OCB wins
            }
            return 0; // If no OCB, consider them tied
        }
        
        if ($a['score'] === $b['score']) {
            return ($a['total_strokes'] < $b['total_strokes']) ? -1 : 1;
        }
        return ($a['score'] < $b['score']) ? -1 : 1;
    }
});

// ---------------------------------------------------------------------
// Assign positions based on sorted scores
// ---------------------------------------------------------------------
$position = 1;
$positions = [];
$previous_score = null;
$previous_strokes = null;
$previous_nett = null;
$previous_hcp_of_day = null;
$previous_stableford_points = null;

// First pass: assign positions to players with real scores
foreach ($player_scores as $i => &$plr) {
    if (isNoScore($plr)) {
        $plr['position'] = null; // Mark for later
        continue;
    }
    if ($mode === 'NETT' && $previous_nett !== null && $plr['nett'] === $previous_nett) {
        $plr['position'] = $positions[$i - 1];
    } else if ($mode === 'SYSTEM36' && $previous_hcp_of_day !== null && $plr['hcp_of_day'] === $previous_hcp_of_day) {
        $plr['position'] = $positions[$i - 1];
    } else if ($mode === 'STABLEFORD' && $previous_stableford_points !== null && $plr['stableford_points'] === $previous_stableford_points) {
        $plr['position'] = $positions[$i - 1];
    } else if ($mode === 'GROSS' && $previous_score !== null && $previous_strokes !== null &&
        $plr['score'] === $previous_score && $plr['total_strokes'] === $previous_strokes) {
        $plr['position'] = $positions[$i - 1];
    } else {
        $plr['position'] = (string)$position;
    }
    $positions[] = $plr['position'];
    $position++;

    if ($mode === 'NETT') {
        $previous_nett = $plr['nett'];
    } else if ($mode === 'SYSTEM36') {
        $previous_hcp_of_day = $plr['hcp_of_day'];
    } else if ($mode === 'STABLEFORD') {
        $previous_stableford_points = $plr['stableford_points'];
    } else {
        $previous_score = $plr['score'];
        $previous_strokes = $plr['total_strokes'];
    }
}
unset($plr);

// Second pass: assign positions to no-score players
foreach ($player_scores as &$plr) {
    if ($plr['position'] === null) {
        $plr['position'] = (string)$position;
        $positions[] = $plr['position'];
        $position++;
    }
}
unset($plr);

// ---------------------------------------------------------------------
// Add tie indicator for tied positions
// ---------------------------------------------------------------------
for ($i = 0; $i < count($player_scores) - 1; $i++) {
    if ($mode === 'NETT' && $player_scores[$i]['nett'] == $player_scores[$i + 1]['nett']) {
        if (strpos($player_scores[$i]['position'], 'T') !== 0) {
            $player_scores[$i]['position'] = 'T' . $player_scores[$i]['position'];
        }
        if (strpos($player_scores[$i + 1]['position'], 'T') !== 0) {
            $player_scores[$i + 1]['position'] = 'T' . $player_scores[$i + 1]['position'];
        }
    } else if ($mode === 'SYSTEM36' && $player_scores[$i]['hcp_of_day'] == $player_scores[$i + 1]['hcp_of_day']) {
        if (strpos($player_scores[$i]['position'], 'T') !== 0) {
            $player_scores[$i]['position'] = 'T' . $player_scores[$i]['position'];
        }
        if (strpos($player_scores[$i + 1]['position'], 'T') !== 0) {
            $player_scores[$i + 1]['position'] = 'T' . $player_scores[$i + 1]['position'];
        }
    } else if ($mode === 'STABLEFORD' && $player_scores[$i]['stableford_points'] == $player_scores[$i + 1]['stableford_points']) {
        if (strpos($player_scores[$i]['position'], 'T') !== 0) {
            $player_scores[$i]['position'] = 'T' . $player_scores[$i]['position'];
        }
        if (strpos($player_scores[$i + 1]['position'], 'T') !== 0) {
            $player_scores[$i + 1]['position'] = 'T' . $player_scores[$i + 1]['position'];
        }
    } else if ($mode === 'GROSS' && $player_scores[$i]['score'] == $player_scores[$i + 1]['score'] &&
        $player_scores[$i]['total_strokes'] == $player_scores[$i + 1]['total_strokes']) {
        if (strpos($player_scores[$i]['position'], 'T') !== 0) {
            $player_scores[$i]['position'] = 'T' . $player_scores[$i]['position'];
        }
        if (strpos($player_scores[$i + 1]['position'], 'T') !== 0) {
            $player_scores[$i + 1]['position'] = 'T' . $player_scores[$i + 1]['position'];
        }
    }
}


function getCountryFlag($inputCountry) {
    // Convert the country name to ISO 3166-1 alpha-2 code
    $countryCodes = [
        'afghanistan' => 'af',
        'albania' => 'al',
        'algeria' => 'dz',
        'andorra' => 'ad',
        'angola' => 'ao',
        'antigua and barbuda' => 'ag',
        'argentina' => 'ar',
        'armenia' => 'am',
        'australia' => 'au',
        'austria' => 'at',
        'azerbaijan' => 'az',
        'bahamas' => 'bs',
        'bahrain' => 'bh',
        'bangladesh' => 'bd',
        'barbados' => 'bb',
        'belarus' => 'by',
        'belgium' => 'be',
        'belize' => 'bz',
        'benin' => 'bj',
        'bhutan' => 'bt',
        'bolivia' => 'bo',
        'bosnia and herzegovina' => 'ba',
        'botswana' => 'bw',
        'brazil' => 'br',
        'brunei' => 'bn',
        'bulgaria' => 'bg',
        'burkina faso' => 'bf',
        'burundi' => 'bi',
        'cabo verde' => 'cv',
        'cambodia' => 'kh',
        'cameroon' => 'cm',
        'canada' => 'ca',
        'central african republic' => 'cf',
        'chad' => 'td',
        'chile' => 'cl',
        'china' => 'cn',
        'colombia' => 'co',
        'comoros' => 'km',
        'congo (congo-brazzaville)' => 'cg',
        'costa rica' => 'cr',
        'croatia' => 'hr',
        'cuba' => 'cu',
        'cyprus' => 'cy',
        'czechia' => 'cz',
        'democratic republic of the congo' => 'cd',
        'denmark' => 'dk',
        'djibouti' => 'dj',
        'dominica' => 'dm',
        'dominican republic' => 'do',
        'ecuador' => 'ec',
        'egypt' => 'eg',
        'el salvador' => 'sv',
        'equatorial guinea' => 'gq',
        'eritrea' => 'er',
        'estonia' => 'ee',
        'eswatini' => 'sz',
        'ethiopia' => 'et',
        'fiji' => 'fj',
        'finland' => 'fi',
        'france' => 'fr',
        'gabon' => 'ga',
        'gambia' => 'gm',
        'georgia' => 'ge',
        'germany' => 'de',
        'ghana' => 'gh',
        'greece' => 'gr',
        'grenada' => 'gd',
        'guatemala' => 'gt',
        'guinea' => 'gn',
        'guinea-bissau' => 'gw',
        'guyana' => 'gy',
        'haiti' => 'ht',
        'honduras' => 'hn',
        'hong kong' => 'hk',
        'hungary' => 'hu',
        'iceland' => 'is',
        'india' => 'in',
        'indonesia' => 'id',
        'iran' => 'ir',
        'iraq' => 'iq',
        'ireland' => 'ie',
        'israel' => 'il',
        'italy' => 'it',
        'jamaica' => 'jm',
        'japan' => 'jp',
        'jordan' => 'jo',
        'kazakhstan' => 'kz',
        'kenya' => 'ke',
        'kiribati' => 'ki',
        'kuwait' => 'kw',
        'kyrgyzstan' => 'kg',
        'laos' => 'la',
        'latvia' => 'lv',
        'lebanon' => 'lb',
        'lesotho' => 'ls',
        'liberia' => 'lr',
        'libya' => 'ly',
        'liechtenstein' => 'li',
        'lithuania' => 'lt',
        'luxembourg' => 'lu',
        'madagascar' => 'mg',
        'malawi' => 'mw',
        'malaysia' => 'my',
        'maldives' => 'mv',
        'mali' => 'ml',
        'malta' => 'mt',
        'marshall islands' => 'mh',
        'mauritania' => 'mr',
        'mauritius' => 'mu',
        'mexico' => 'mx',
        'micronesia' => 'fm',
        'moldova' => 'md',
        'monaco' => 'mc',
        'mongolia' => 'mn',
        'montenegro' => 'me',
        'morocco' => 'ma',
        'mozambique' => 'mz',
        'namibia' => 'na',
        'nauru' => 'nr',
        'nepal' => 'np',
        'netherlands' => 'nl',
        'new zealand' => 'nz',
        'nicaragua' => 'ni',
        'niger' => 'ne',
        'nigeria' => 'ng',
        'north korea' => 'kp',
        'north macedonia' => 'mk',
        'norway' => 'no',
        'oman' => 'om',
        'pakistan' => 'pk',
        'palau' => 'pw',
        'palestine state' => 'ps',
        'panama' => 'pa',
        'papua new guinea' => 'pg',
        'paraguay' => 'py',
        'peru' => 'pe',
        'philippines' => 'ph',
        'poland' => 'pl',
        'portugal' => 'pt',
        'qatar' => 'qa',
        'romania' => 'ro',
        'russia' => 'ru',
        'rwanda' => 'rw',
        'saint kitts and nevis' => 'kn',
        'saint lucia' => 'lc',
        'saint vincent and the grenadines' => 'vc',
        'samoa' => 'ws',
        'san marino' => 'sm',
        'sao tome and principe' => 'st',
        'saudi arabia' => 'sa',
        'senegal' => 'sn',
        'serbia' => 'rs',
        'seychelles' => 'sc',
        'sierra leone' => 'sl',
        'singapore' => 'sg',
        'slovakia' => 'sk',
        'slovenia' => 'si',
        'solomon islands' => 'sb',
        'somalia' => 'so',
        'south africa' => 'za',
        'south korea' => 'kr',
        'south sudan' => 'ss',
        'spain' => 'es',
        'sri lanka' => 'lk',
        'sudan' => 'sd',
        'suriname' => 'sr',
        'sweden' => 'se',
        'switzerland' => 'ch',
        'syria' => 'sy',
        'tajikistan' => 'tj',
        'tanzania' => 'tz',
        'thailand' => 'th',
        'timor-leste' => 'tl',
        'togo' => 'tg',
        'tonga' => 'to',
        'trinidad and tobago' => 'tt',
        'tunisia' => 'tn',
        'turkey' => 'tr',
        'turkmenistan' => 'tm',
        'tuvalu' => 'tv',
        'uganda' => 'ug',
        'ukraine' => 'ua',
        'united arab emirates' => 'ae',
        'united kingdom' => 'gb',
        'united states' => 'us',
        'uruguay' => 'uy',
        'uzbekistan' => 'uz',
        'vanuatu' => 'vu',
        'vatican city' => 'va',
        'venezuela' => 've',
        'vietnam' => 'vn',
        'yemen' => 'ye',
        'zambia' => 'zm',
        'zimbabwe' => 'zw',
        'indonesia'=> 'id',
        'taiwan' => 'tw',
        'myanmar' => 'mm',
    ];

    // Convert the input to lowercase for case-insensitive matching
    $inputCountryLower = strtolower($inputCountry);

    // Check if the country exists in the array
    if (array_key_exists($inputCountryLower, $countryCodes)) {
        $countryCode = $countryCodes[$inputCountryLower];
        return "https://flagcdn.com/48x36/{$countryCode}.png"; // Adjust the URL path based on your source
    } else {
        return "Country not found.";
    }
}

$query11 = "
    SELECT ci.course_name 
FROM course_info ci
INNER JOIN custom_hole ch ON ci.course_id = ch.course_id
WHERE ch.category_id = ?;
";

// Prepare the SQL statement
$stmt11 = $conn->prepare($query11);

// Bind the parameter
$stmt11->bind_param("s", $category_id);

// Execute the query
$stmt11->execute();

// Get the result
$result11 = $stmt11->get_result();

// Fetch the course_name
if ($row11 = $result11->fetch_assoc()) {
    $course_name = $row11['course_name'];
} else {
    echo "No course found for the given category.";
}

// Close the statement
$stmt11->close();

// Ensure no stale data is carried over for subsequent queries
$conn->next_result();



// Mode and nett format already fetched above

// System 36 point table based on nett score
function getSystem36Points($nettScore) {
    if ($nettScore <= 0) return 2;  // Par or better = 2 points
    if ($nettScore == 1) return 1;  // Bogey = 1 point
    return 0;                       // Double bogey or worse = 0 points
}

function getStablefordPoints($nettScore) {
    if ($nettScore <= -4) {
        return 6; // -4 or better = 6 points
    } elseif ($nettScore == -3) {
        return 5; // -3 = 5 points  
    } elseif ($nettScore == -2) {
        return 4; // -2 = 4 points
    } elseif ($nettScore == -1) {
        return 3; // -1 = 3 points
    } elseif ($nettScore == 0) {
        return 2; // 0 = 2 points
    } elseif ($nettScore == 1) {
        return 1; // 1 = 1 point
        } else {
        return 0; // 2 or worse = 0 points
        }
}

// Fetch sponsor logos for the event
$sponsor_logos = [];
$sponsor_logos_query = "
    SELECT logo 
    FROM sponsor_logo 
    WHERE event_mgm_id = ?
";
$sponsor_logos_stmt = $conn->prepare($sponsor_logos_query);
$sponsor_logos_stmt->bind_param("s", $event_mgm_id);
$sponsor_logos_stmt->execute();
$sponsor_logos_result = $sponsor_logos_stmt->get_result();
while ($sponsor_logo_row = $sponsor_logos_result->fetch_assoc()) {
    if (!empty($sponsor_logo_row['logo'])) {
        $sponsor_logos[] = $sponsor_logo_row['logo'];
    }
}
$sponsor_logos_stmt->close();

// Close the database connection (optional, but recommended)
$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Result Details</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css" />
    <link rel="icon" type="image/x-icon" href="img/sportexcel.ico">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
    <style>
        /* Center align all numeric columns */
        .numeric-center {
            text-align: center !important;
        }
        
        /* Center align specific table columns */
        table td:nth-child(1),  /* Position */
        table td:nth-child(3),  /* HCP */
        table td:nth-child(4),  /* HCP of Day */
        table td:nth-child(5),  /* ToPar */
        table td:nth-child(6),  /* Hole */
        table td:nth-child(7),  /* Round 1 */
        table td:nth-child(8),  /* Round 2 */
        table td:nth-child(9),  /* Round 3 */
        table td:nth-child(10), /* Round 4 */
        table td:nth-child(11), /* Total */
        table td:nth-child(12), /* Nett/Points */
        table th:nth-child(1),  /* Position header */
        table th:nth-child(3),  /* HCP header */
        table th:nth-child(4),  /* HCP of Day header */
        table th:nth-child(5),  /* ToPar header */
        table th:nth-child(6),  /* Hole header */
        table th:nth-child(7),  /* Round headers */
        table th:nth-child(8),
        table th:nth-child(9),
        table th:nth-child(10),
        table th:nth-child(11), /* Total header */
        table th:nth-child(12)  /* Nett/Points header */
        {
            text-align: center !important;
        }
    </style>
    <link rel="stylesheet" href="css/bootstrap-testimonial-slider.min.css" />
    <style>
        .zoom:hover {
            transform: scale(1.05);
            transition: transform 0.3s ease-in-out;
        }
        .carousel-control-prev,
        .carousel-control-next {
            width: 5%;
        }
        .carousel-control-prev-icon,
        .carousel-control-next-icon {
            background-color: black;
        }
        @media (max-width: 768px) {
            .carousel-control-prev,
            .carousel-control-next {
                width: 10%;
                top: 50%;
                transform: translateY(-50%);
            }
            .carousel-control-prev-icon,
            .carousel-control-next-icon {
                background-size: 50%, 50%;
            }
        }
        .fade-in {
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }
        .fade-in.visible {
            opacity: 1;
        }
        .header-section {
            background-color: #f9f9f9;
            padding: 20px 0;
            width: 100%;
            text-align: center;
        }
        .main-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 24px;
            margin-bottom: 20px;
        }
        .dataTables_filter {
            float: left !important;
            margin-bottom: 20px;
        }
        .dataTables_length {
            float: right !important;
        }
        .dataTables_info {
            float: left !important;
        }
        .dataTables_paginate {
            float: right !important;
        }

        /* Enhanced search styling */
        .dataTables_filter input {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 14px;
            width: 300px;
            transition: all 0.3s ease;
        }

        .dataTables_filter input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            outline: none;
        }

        .dataTables_filter label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 5px;
        }

        .search-message {
            max-width: 400px;
            font-size: 14px;
        }
        @media (min-width: 768px) {
            .mobile-only {
                display: none;
            }
        }
        @media (max-width: 767px) {
            .desktop-only {
                display: none;
            }
        }
        td.details-control {
            cursor: pointer;
            text-align: center;
        }
        td.details-control::before {
            font-family: "Font Awesome 5 Free";
            content: "\f0fe"; /* fa-plus-square */
            font-weight: 900;
        }
        tr.shown td.details-control::before {
            content: "\f146"; /* fa-minus-square */
        }
        /* Adjust the styling of the expanded row */
        table.child-table {
            width: 100%;
            border-collapse: collapse;
        }
        table.child-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        /* Logo styles */
        .logo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
            padding: 12px;
            background: white;
            margin-bottom: 10px;
        }
        
        .logo-item {
            max-width: 100px;
            max-height: 60px;
            object-fit: contain;
        }
        
        @media (max-width: 768px) {
            .logo-container {
                gap: 10px;
                padding: 8px;
            }
            
            .logo-item {
                max-width: 70px;
                max-height: 45px;
            }
        }
        
        /* For small screens with many logos */
        @media (max-width: 480px) {
            .logo-container {
                justify-content: space-around;
            }
            
            .logo-item {
                max-width: 50px;
                max-width: 60px;
                margin-bottom: 8px;
            }
        }
        
        /* Sponsor logos section styles */
        .sponsor-logos-section {
            border-top: 1px solid #e9ecef;
        }
        
        .sponsor-logo-item {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-radius: 8px;
            padding: 10px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: fit-content;
            height: fit-content;
        }
        
        .sponsor-logo-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .sponsor-logo-img {
            transition: all 0.3s ease;
        }
        
        .sponsor-logo-img:hover {
            transform: scale(1.05);
        }
        
        /* Responsive design for sponsor logos */
        @media (max-width: 768px) {
            .sponsor-logos-container {
                gap: 15px !important;
                padding: 15px !important;
            }
            
            .sponsor-logo-img {
                max-width: 100px !important;
                max-height: 70px !important;
            }
            
            .sponsor-logo-item {
                padding: 8px !important;
            }
        }
        
        @media (max-width: 480px) {
            .sponsor-logos-container {
                gap: 10px !important;
                padding: 10px !important;
            }
            
            .sponsor-logo-img {
                max-width: 80px !important;
                max-height: 60px !important;
            }
            
            .sponsor-logo-item {
                padding: 6px !important;
            }
            
            .sponsor-logos-section h3 {
                font-size: 20px !important;
            }
            
            .sponsor-logos-section p {
                font-size: 12px !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <?php include("header.php"); ?>
    
    <!-- Logo Section -->
    <?php if (!empty($logos)): ?>
    <div class="logo-container">
        <?php foreach ($logos as $logo): ?>
            <img src="img/event_logo/<?php echo htmlspecialchars($logo); ?>" alt="Event sponsor logo" class="logo-item">
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    
    <!-- Main Content -->
    <div class="header-section fade-in">
        <div class="container text-center">
            <h1 id="event-name" class="main-title">Final Results: <?php echo htmlspecialchars($event_details['event_name']); ?></h1>
            <?php
                            $formatted_start_date = (new DateTime($event_details['event_start_date']))->format('d-m-Y');
                            $formatted_end_date = (new DateTime($event_details['event_end_date']))->format('d-m-Y');
                            ?>
            <p id="event-details" class="subtitle"><?php echo htmlspecialchars($formatted_start_date) . " - " . ($formatted_end_date); ?><br><?php echo htmlspecialchars($event_details['event_venue']); ?><br><?php echo "<small>($course_name | Mode of Play: $mode)</small>"; ?></p>
           
        </div>
    </div>
    <div class="container mx-auto my-8 fade-in">
        <!-- Category Selection -->
        <form method="get" action="result_details.php">
            <input type="hidden" name="event_mgm_id" value="<?php echo htmlspecialchars($event_mgm_id); ?>">
            <div class="mb-4">
                <label for="category_id" class="form-label">Select Category:</label>
                <select name="category_id" id="category_id" class="form-control" onchange="this.form.submit()">
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo htmlspecialchars($category['category_id']); ?>" <?php echo $category['category_id'] == $category_id ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['category_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </form>
        <div class="desktop-only">
            <div class="table-responsive">
                <table id="scoresTableDesktop" class="display">
                    <thead id="scoresTableDesktopHead">
                    <tr>
                        <th>Position</th>
                        <th>Player</th>
                        <?php
                         if ($mode === 'NETT' || $mode === 'SYSTEM36' || $mode === 'STABLEFORD') {
                            echo"<th>HCP</th>";
                         } 
                         if ($mode === 'SYSTEM36') {
                            echo"<th>HCP of Day</th>";
                         } 
                         if ($mode === 'STABLEFORD') {
                            echo"<th>HCP of Day</th>";
                         } 
                        ?>
                        <th>ToPar</th>
                        <th>Hole</th>
                        <?php foreach ($rounds as $round): ?>
                            <th>Round <?php echo htmlspecialchars($round); ?></th>
                        <?php endforeach; ?>
                        <th>Total</th>
                        <?php
                         if ($mode === 'NETT') {
                            echo"<th>Nett</th>";
                         } 
                         if ($mode === 'SYSTEM36') {
                            echo"<th>System36 Points</th>";
                         } 
                         if ($mode === 'STABLEFORD') {
                            echo"<th>Stableford Points</th>";
                         } 
                        ?>
                    </tr>
                </thead>
                <tbody id="scores-body">
                    <?php
                 foreach ($player_scores as $index => $details) {
                     $country = $details['country'];
                     $isHidden = isset($details['hide']) && $details['hide'] == 1;
                     $hideClass = $isHidden ? 'style="opacity: 0.5;"' : '';
                     

                     
                     echo '<tr ' . $hideClass . '>';
                     echo '<td class="numeric-center"><div style="display: flex; justify-content: center;">' . htmlspecialchars($details['position']) . '</div></td>';
                     echo '<td>' . htmlspecialchars($details['name']) . '<br>
                     <img src="' . getCountryFlag($country) . '" alt="Flag"> <span class="text-gray-400">' . htmlspecialchars($details['country']) . '</span></td>'; 
                     
                     if ($mode === 'NETT' || $mode === 'SYSTEM36' || $mode === 'STABLEFORD') {
                         echo '<td class="numeric-center">' . htmlspecialchars($details['handicap']) . '</td>';
                     }
                     if ($mode === 'SYSTEM36') {
                         echo '<td class="numeric-center">' . htmlspecialchars($details['hcp_of_day']) . '</td>';
                     }
                     if ($mode === 'STABLEFORD') {
                         echo '<td class="numeric-center">' . htmlspecialchars($details['hcp_of_day']) . '</td>';
                     }
                     
                     echo '<td class="numeric-center">' . htmlspecialchars($details['score']) . '</td>';
                     echo '<td class="numeric-center">' . htmlspecialchars($details['hole_display']) . '</td>';
                     foreach ($rounds as $round) {
                         echo '<td class="numeric-center"><a href="result_details_round.php?event_id=' . htmlspecialchars($event_id) . '&formid=' . htmlspecialchars($details['id']) . '&round=' . htmlspecialchars($round) . '" style="color: black; text-decoration: underline;"><b>' . (isset($details['rounds'][$round]) ? htmlspecialchars($details['rounds'][$round]) : '-') . '</b></a></td>';
                     }
                 
                     echo '<td class="numeric-center">' . htmlspecialchars($details['total_strokes']) . '</td>';
                     if ($mode === 'NETT') {
                        echo '<td class="numeric-center">' . htmlspecialchars($details['nett']) . '</td>';
                     }
                     if ($mode === 'SYSTEM36') {
                        echo '<td class="numeric-center">' . htmlspecialchars($details['system36_points']) . '</td>';
                     }
                     if ($mode === 'STABLEFORD') {
                        echo '<td class="numeric-center">' . htmlspecialchars($details['stableford_points']) . '</td>';
                     }
                     echo '</tr>';
                 }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="mobile-only">
            <div class="table-responsive">
                <table id="scoresTableMobile" class="display">
                    <thead>
                        <tr>
                            <th></th> <!-- For the expand/collapse control -->
                            <th>Pos</th>
                            <th>Player</th>
                            <?php
                         if ($mode === 'NETT' || $mode === 'SYSTEM36' || $mode === 'STABLEFORD') {
                            echo"<th>HCP</th>";
                         } 
                         if ($mode === 'SYSTEM36') {
                            echo"<th>HCP of Day</th>";
                         } 
                         if ($mode === 'STABLEFORD') {
                            echo"<th>HCP of Day</th>";
                         } 
                        ?>
                            <th>ToPar</th>
                            <th>Hole</th>
                            <th>Total</th>
                            <?php
                             if ($mode === 'NETT') {
                                echo"<th>Nett</th>";
                             } 
                             if ($mode === 'SYSTEM36') {
                                echo"<th>Points</th>";
                             } 
                             if ($mode === 'STABLEFORD') {
                                echo"<th>Stableford Points</th>";
                             } 
                            ?>
                            
                        </tr>
                    </thead>
                    <tbody id="scores-body-mobile">
                        <!-- Rows will be populated via JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
      
    <!-- Sponsor Logos Section -->
    <?php if (!empty($sponsor_logos)): ?>
    <div class="sponsor-logos-section fade-in" style="background-color: #f8f9fa; padding: 30px 0; margin-top: 20px;">
        <div class="container">
            <div class="text-center mb-4">
                <h3 style="color: #333; font-weight: 600; margin-bottom: 10px;">Event Sponsors</h3>
            </div>
            <div class="sponsor-logos-container" style="display: flex; justify-content: center; align-items: center; flex-wrap: wrap; gap: 25px; padding: 20px;">
                <?php foreach ($sponsor_logos as $sponsor_logo): ?>
                    <div class="sponsor-logo-item">
                        <img src="img/event_logo/<?php echo htmlspecialchars($sponsor_logo); ?>" 
                             alt="Sponsor logo" 
                             class="sponsor-logo-img" 
                             style="max-width: 120px; max-height: 80px; object-fit: contain; filter: grayscale(0%); transition: all 0.3s ease;">
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <!-- Footer -->
    <?php include("footer.php"); ?>
  
    
    <script type="text/javascript" src="js/mdb.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/js/all.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script>
        var desktopTableInitialized = false;
        var mobileTableInitialized = false;
        function getCountryFlagUrl(countryName) {
    const countryCodes = {
        'afghanistan': 'af',
        'albania': 'al',
        'algeria': 'dz',
        'andorra': 'ad',
        'angola': 'ao',
        'antigua and barbuda': 'ag',
        'argentina': 'ar',
        'armenia': 'am',
        'australia': 'au',
        'austria': 'at',
        'azerbaijan': 'az',
        'bahamas': 'bs',
        'bahrain': 'bh',
        'bangladesh': 'bd',
        'barbados': 'bb',
        'belarus': 'by',
        'belgium': 'be',
        'belize': 'bz',
        'benin': 'bj',
        'bhutan': 'bt',
        'bolivia': 'bo',
        'bosnia and herzegovina': 'ba',
        'botswana': 'bw',
        'brazil': 'br',
        'brunei': 'bn',
        'bulgaria': 'bg',
        'burkina faso': 'bf',
        'burundi': 'bi',
        'cabo verde': 'cv',
        'cambodia': 'kh',
        'cameroon': 'cm',
        'canada': 'ca',
        'central african republic': 'cf',
        'chad': 'td',
        'chile': 'cl',
        'china': 'cn',
        'colombia': 'co',
        'comoros': 'km',
        'congo (congo-brazzaville)': 'cg',
        'costa rica': 'cr',
        'croatia': 'hr',
        'cuba': 'cu',
        'cyprus': 'cy',
        'czechia': 'cz',
        'democratic republic of the congo': 'cd',
        'denmark': 'dk',
        'djibouti': 'dj',
        'dominica': 'dm',
        'dominican republic': 'do',
        'ecuador': 'ec',
        'egypt': 'eg',
        'el salvador': 'sv',
        'equatorial guinea': 'gq',
        'eritrea': 'er',
        'estonia': 'ee',
        'eswatini': 'sz',
        'ethiopia': 'et',
        'fiji': 'fj',
        'finland': 'fi',
        'france': 'fr',
        'gabon': 'ga',
        'gambia': 'gm',
        'georgia': 'ge',
        'germany': 'de',
        'ghana': 'gh',
        'greece': 'gr',
        'grenada': 'gd',
        'guatemala': 'gt',
        'guinea': 'gn',
        'guinea-bissau': 'gw',
        'guyana': 'gy',
        'haiti': 'ht',
        'honduras': 'hn',
        'hong kong': 'hk',
        'hungary': 'hu',
        'iceland': 'is',
        'india': 'in',
        'indonesia': 'id',
        'iran': 'ir',
        'iraq': 'iq',
        'ireland': 'ie',
        'israel': 'il',
        'italy': 'it',
        'jamaica': 'jm',
        'japan': 'jp',
        'jordan': 'jo',
        'kazakhstan': 'kz',
        'kenya': 'ke',
        'kiribati': 'ki',
        'kuwait': 'kw',
        'kyrgyzstan': 'kg',
        'laos': 'la',
        'latvia': 'lv',
        'lebanon': 'lb',
        'lesotho': 'ls',
        'liberia': 'lr',
        'libya': 'ly',
        'liechtenstein': 'li',
        'lithuania': 'lt',
        'luxembourg': 'lu',
        'madagascar': 'mg',
        'malawi': 'mw',
        'malaysia': 'my',
        'maldives': 'mv',
        'mali': 'ml',
        'malta': 'mt',
        'marshall islands': 'mh',
        'mauritania': 'mr',
        'mauritius': 'mu',
        'mexico': 'mx',
        'micronesia': 'fm',
        'moldova': 'md',
        'monaco': 'mc',
        'mongolia': 'mn',
        'montenegro': 'me',
        'morocco': 'ma',
        'mozambique': 'mz',
        'namibia': 'na',
        'nauru': 'nr',
        'nepal': 'np',
        'netherlands': 'nl',
        'new zealand': 'nz',
        'nicaragua': 'ni',
        'niger': 'ne',
        'nigeria': 'ng',
        'north korea': 'kp',
        'north macedonia': 'mk',
        'norway': 'no',
        'oman': 'om',
        'pakistan': 'pk',
        'palau': 'pw',
        'palestine state': 'ps',
        'panama': 'pa',
        'papua new guinea': 'pg',
        'paraguay': 'py',
        'peru': 'pe',
        'philippines': 'ph',
        'poland': 'pl',
        'portugal': 'pt',
        'qatar': 'qa',
        'romania': 'ro',
        'russia': 'ru',
        'rwanda': 'rw',
        'saint kitts and nevis': 'kn',
        'saint lucia': 'lc',
        'saint vincent and the grenadines': 'vc',
        'samoa': 'ws',
        'san marino': 'sm',
        'sao tome and principe': 'st',
        'saudi arabia': 'sa',
        'senegal': 'sn',
        'serbia': 'rs',
        'seychelles': 'sc',
        'sierra leone': 'sl',
        'singapore': 'sg',
        'slovakia': 'sk',
        'slovenia': 'si',
        'solomon islands': 'sb',
        'somalia': 'so',
        'south africa': 'za',
        'south korea': 'kr',
        'south sudan': 'ss',
        'spain': 'es',
        'sri lanka': 'lk',
        'sudan': 'sd',
        'suriname': 'sr',
        'sweden': 'se',
        'switzerland': 'ch',
        'syria': 'sy',
        'tajikistan': 'tj',
        'tanzania': 'tz',
        'thailand': 'th',
        'timor-leste': 'tl',
        'togo': 'tg',
        'tonga': 'to',
        'trinidad and tobago': 'tt',
        'tunisia': 'tn',
        'turkey': 'tr',
        'turkmenistan': 'tm',
        'tuvalu': 'tv',
        'uganda': 'ug',
        'ukraine': 'ua',
        'united arab emirates': 'ae',
        'united kingdom': 'gb',
        'united states': 'us',
        'uruguay': 'uy',
        'uzbekistan': 'uz',
        'vanuatu': 'vu',
        'vatican city': 'va',
        'venezuela': 've',
        'vietnam': 'vn',
        'yemen': 'ye',
        'zambia': 'zm',
        'zimbabwe': 'zw',
        'myanmar': 'mm',
        'taiwan': 'tw',
        'indonesia': 'id'
    };

    const normalizedCountry = countryName.toLowerCase();
    const countryCode = countryCodes[normalizedCountry];
    return countryCode ? `https://flagcdn.com/48x36/${countryCode}.png` : null;
}
        function fetchAndUpdateData() {
            const eventMgmId = '<?php echo $event_mgm_id; ?>';
            const categoryId = document.getElementById('category_id').value;

            $.ajax({
                url: 'fetch_data.php',
                type: 'GET',
                data: {
                    event_mgm_id: eventMgmId,
                    category_id: categoryId,
                    event_id: '<?php echo $event_id; ?>'
                },
                dataType: 'json',
                success: function(data) {
                    const rounds = data.rounds;
                    const playerScores = data.player_scores;
                    const mode = data.mode || 'GROSS';

                    // Clear the current tables
                  if ($.fn.DataTable.isDataTable('#scoresTableDesktop')) { // Check if DataTable already exists
        $('#scoresTableDesktop').DataTable().destroy(); // Destroy existing DataTable
}
                    if ($.fn.DataTable.isDataTable('#scoresTableMobile')) { // Check if DataTable already exists
    $('#scoresTableMobile').DataTable().destroy(); // Destroy existing DataTable
}

                    $('#scoresTableDesktop thead').empty();
                    $('#scoresTableDesktop tbody').empty();
                    $('#scoresTableMobile tbody').empty();

                    // Generate desktop table headers
                    let desktopHeaderHtml = '<tr>';
                    desktopHeaderHtml += '<th>Position</th>';
                    desktopHeaderHtml += '<th>Player</th>';
                    if (mode === 'NETT' || mode === 'SYSTEM36' || mode === 'STABLEFORD') {
                        desktopHeaderHtml += '<th>HCP</th>';
                    }
                    if (mode === 'SYSTEM36') {
                        desktopHeaderHtml += '<th>HCP of Day</th>';
                    }
                    if (mode === 'STABLEFORD') {
                        desktopHeaderHtml += '<th>HCP of Day</th>';
                    }
                    desktopHeaderHtml += '<th>ToPar</th>';
                    desktopHeaderHtml += '<th>Hole</th>';
                    rounds.forEach(round => {
                        desktopHeaderHtml += '<th>Round ' + round + '</th>';
                    });
                    desktopHeaderHtml += '<th>Total</th>';
                    if (mode === 'NETT') {
                        desktopHeaderHtml += '<th>Nett</th>';
                    }
                    if (mode === 'SYSTEM36') {
                        desktopHeaderHtml += '<th>Points</th>';
                    }
                    if (mode === 'STABLEFORD') {
                        desktopHeaderHtml += '<th>Stableford Points</th>';
                    }
                    desktopHeaderHtml += '</tr>';

                    $('#scoresTableDesktop thead').html(desktopHeaderHtml);

                    // Generate desktop table rows
                    playerScores.forEach(details => {
                    const flagUrl = getCountryFlagUrl(details.country);
                    const flagImg = flagUrl ? `<img src="${flagUrl}" alt="Flag of ${details.country}" style="width: 24px; margin-right: 5px;">` : '';
                    const isHidden = details.hide === 1;
                    const status = isHidden ? getPlayerStatus(details.hide_rmk) : '';
                    const statusBadge = status ? createStatusBadge(status) : '';
                    
                    let rowHtmlDesktop = '<tr>';
                    rowHtmlDesktop += '<td>' + details.position + '</td>';
                    
                    // Changed: Remove text-decoration, keep only color change
                    const nameStyle = isHidden ? 'style="color: #999;"' : '';
                    rowHtmlDesktop += `<td ${nameStyle}>${details.name}${statusBadge}<br>
                        <span class="text-gray-400">
                            <span style="display: inline-flex; align-items: center;">
                                ${flagImg} ${details.country}
                            </span>
                        </span>
                    </td>`;

                    if (mode === 'NETT' || mode === 'SYSTEM36' || mode === 'STABLEFORD') {
                        rowHtmlDesktop += `<td ${nameStyle}>` + (details.handicap ?? '-') + '</td>';
                    }
                    if (mode === 'SYSTEM36') {
                        rowHtmlDesktop += `<td ${nameStyle}>` + (details.hcp_of_day ?? '-') + '</td>';
                    }
                    if (mode === 'STABLEFORD') {
                        rowHtmlDesktop += `<td ${nameStyle}>` + (details.hcp_of_day ?? '-') + '</td>';
                    }
                    rowHtmlDesktop += `<td ${nameStyle}>` + details.score + '</td>';
                    rowHtmlDesktop += `<td ${nameStyle}>` + details.hole_display + '</td>';

                    rounds.forEach(round => {
                        const roundScore = details.rounds[round] ? details.rounds[round] : '-';
                        const linkColor = isHidden ? '#999' : 'black';
                        rowHtmlDesktop += `<td ${nameStyle}><a href="result_details_round.php?event_id=<?php echo $event_id; ?>&formid=${details.id}&round=${round}" style="color: ${linkColor}; text-decoration: underline;"><b>${roundScore}</b></a></td>`;
                    });

                    rowHtmlDesktop += `<td ${nameStyle}>` + details.total_strokes + '</td>';
                    if (mode === 'NETT') {
                        rowHtmlDesktop += `<td ${nameStyle}>` + details.nett + '</td>';
                    }
                    if (mode === 'SYSTEM36') {
                        rowHtmlDesktop += `<td ${nameStyle}>` + (details.system36_points ?? '-') + '</td>';
                    }
                    if (mode === 'STABLEFORD') {
                        rowHtmlDesktop += `<td ${nameStyle}>` + (details.stableford_points ?? '-') + '</td>';
                    }
                    rowHtmlDesktop += '</tr>';

                    $('#scoresTableDesktop tbody').append(rowHtmlDesktop);
                });

                    // Initialize desktop DataTable with enhanced search
                    var desktopTable = $('#scoresTableDesktop').DataTable({
                        "paging": false,
                        "info": false,
                        "columnDefs": [
                            { "type": "num", "targets": 0 }
                        ],
                        "dom": '<"top"f>rt<"bottom"><"clear">'
                    });

                    // Enhanced cross-category search functionality
                    $('.dataTables_filter input').attr('placeholder', 'Search player across all categories...');

                    // Check if there's a search parameter in URL and apply it
                    var urlParams = new URLSearchParams(window.location.search);
                    var searchParam = urlParams.get('search');
                    if (searchParam) {
                        $('.dataTables_filter input').val(searchParam);
                        desktopTable.search(searchParam).draw();
                        // Clear the search parameter from URL without refreshing
                        var newUrl = window.location.pathname + '?event_mgm_id=<?php echo $event_mgm_id; ?>&category_id=' + $('#category').val();
                        window.history.replaceState({}, document.title, newUrl);
                    }

                    // Override the default search to include cross-category functionality
                    $('.dataTables_filter input').off('keyup search input').on('keyup search input', function() {
                        var searchTerm = this.value.toLowerCase().trim();

                        if (searchTerm.length >= 2) {
                            // First search in current table
                            desktopTable.search(searchTerm).draw();

                            // If no results found in current category, search across all categories
                            if (desktopTable.rows({search: 'applied'}).count() === 0 && searchTerm.length >= 3) {
                                showSearchMessage('Searching across all categories...', 'info');
                                searchAcrossCategories(searchTerm);
                            }
                        } else {
                            desktopTable.search('').draw();
                        }
                    });

                    desktopTableInitialized = true;

                    // Function to search across all categories
                    function searchAcrossCategories(searchTerm) {
                        var eventMgmId = '<?php echo $event_mgm_id; ?>';
                        var categories = <?php echo json_encode($categories); ?>;

                        $.ajax({
                            url: 'search_player_across_categories.php',
                            type: 'POST',
                            data: {
                                search_term: searchTerm,
                                event_mgm_id: eventMgmId,
                                categories: categories
                            },
                            dataType: 'json',
                            success: function(response) {
                                // Clear the searching message
                                $('.search-message').remove();

                                if (response.found && response.category_id !== $('#category').val()) {
                                    // Show success message briefly before redirect
                                    showSearchMessage(`Found "${response.player_name}" in ${response.category_name}. Redirecting...`, 'success');

                                    // Redirect after a short delay to show the message
                                    setTimeout(function() {
                                        window.location.href = `result_details.php?event_mgm_id=${eventMgmId}&category_id=${response.category_id}&search=${encodeURIComponent(searchTerm)}`;
                                    }, 1000);
                                } else if (response.found && response.category_id === $('#category').val()) {
                                    // Player found in current category, just highlight the result
                                    showSearchMessage(`Found "${response.player_name}" in current category.`, 'success');
                                } else if (!response.found) {
                                    // Show no results message
                                    showSearchMessage('No player found with that name across all categories.', 'warning');
                                }
                            },
                            error: function() {
                                $('.search-message').remove();
                                showSearchMessage('Error searching across categories', 'warning');
                            }
                        });
                    }

                    // Function to show search messages
                    function showSearchMessage(message, type = 'info') {
                        // Remove existing message
                        $('.search-message').remove();

                        var alertClass = 'alert-info';
                        var icon = '<i class="fas fa-info-circle me-2"></i>';

                        if (type === 'success') {
                            alertClass = 'alert-success';
                            icon = '<i class="fas fa-check-circle me-2"></i>';
                        } else if (type === 'warning') {
                            alertClass = 'alert-warning';
                            icon = '<i class="fas fa-exclamation-triangle me-2"></i>';
                        }

                        // Add message below search box
                        $('.dataTables_filter').after(`
                            <div class="search-message alert ${alertClass} alert-dismissible fade show mt-2" role="alert">
                                ${icon}${message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        `);

                        // Auto-hide after 3 seconds for info messages, 5 seconds for others
                        var hideDelay = type === 'info' ? 3000 : 5000;
                        setTimeout(function() {
                            $('.search-message').fadeOut();
                        }, hideDelay);
                    }

                    // Generate mobile table rows
                    playerScores.forEach(details => {
                        const holeDisplay = (details.hole_display !== undefined && details.hole_display !== null) ? details.hole_display : '-';
                        const flagUrlMobile = getCountryFlagUrl(details.country);
                        const flagImgMobile = flagUrlMobile ? `<img src="${flagUrlMobile}" alt="Flag of ${details.country}" style="width: 24px; margin-right: 5px;">` : '';
                        const isHidden = details.hide === 1;
                        const status = isHidden ? getPlayerStatus(details.hide_rmk) : '';
                        const statusBadge = status ? createStatusBadge(status) : '';
                        
                        // Changed: Remove text-decoration, keep only color change
                        const rowStyle = isHidden ? 'style="color: #999;"' : '';
                        
                        let rowHtmlMobile = '<tr>';
                        rowHtmlMobile += '<td class="details-control"></td>';
                        rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + details.position + '</td>';
                        rowHtmlMobile += `<td ${rowStyle}>${flagImgMobile}${details.name}${statusBadge}</td>`;
                        if (mode === 'NETT' || mode === 'SYSTEM36' || mode === 'STABLEFORD') {
                            rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + (details.handicap ?? '-') + '</td>';
                        }
                        if (mode === 'SYSTEM36') {
                            rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + (details.hcp_of_day ?? '-') + '</td>';
                        }
                        if (mode === 'STABLEFORD') {
                            rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + (details.hcp_of_day ?? '-') + '</td>';
                        }
                        rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + details.score + '</td>';
                        rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + holeDisplay + '</td>';
                        rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + details.total_strokes + '</td>';
                        
                        if (mode === 'NETT') {
                            rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + details.nett + '</td>';
                        }
                        if (mode === 'SYSTEM36') {
                            rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + (details.system36_points ?? '-') + '</td>';
                        }
                        if (mode === 'STABLEFORD') {
                            rowHtmlMobile += `<td class="numeric-center" ${rowStyle}>` + (details.stableford_points ?? '-') + '</td>';
                        }
                        rowHtmlMobile += '</tr>';

                        $('#scoresTableMobile tbody').append(rowHtmlMobile);
                        
                        // Store rounds data for expandable rows
                        $('#scoresTableMobile tbody tr:last').data('rounds', {
                            id: details.id,
                            rounds: details.rounds,
                            roundsArray: rounds,
                            isHidden: isHidden
                        });
                    });

                    // Initialize mobile DataTable with expandable rows
                    var mobileColumns = [
                        { "orderable": false }, // the expand/collapse icon
                        null,                   // Position
                        null,                   // Player
                    ];
                    
                    // Add HCP column if needed
                    if (mode === 'NETT' || mode === 'SYSTEM36' || mode === 'STABLEFORD') {
                        mobileColumns.push(null); // HCP
                    }
                    
                    // Add mode-specific columns
                    if (mode === 'SYSTEM36' || mode === 'STABLEFORD') {
                        mobileColumns.push(null); // HCP of Day
                    }
                    
                    // Add common columns
                    mobileColumns.push(null); // ToPar
                    mobileColumns.push(null); // Hole
                    mobileColumns.push(null); // Total

                    // Add final scoring column based on mode
                    if (mode === 'NETT') {
                        mobileColumns.push(null); // Nett
                    } else if (mode === 'SYSTEM36') {
                        mobileColumns.push(null); // Points
                    } else if (mode === 'STABLEFORD') {
                        mobileColumns.push(null); // Stableford Points
                    }
                    // GROSS mode doesn't need additional column (just Total)

                    // Then initialize the DataTable:
                    var mobileTable = $('#scoresTableMobile').DataTable({
                        paging: false,
                        info: false,
                        searching: false,
                        ordering: false,
                        columns: mobileColumns
                    });
                    mobileTableInitialized = true;

                    // Add event listener for opening and closing details
                    $('#scoresTableMobile tbody').on('click', 'td.details-control', function () {
                        var tr = $(this).closest('tr');
                        var row = mobileTable.row(tr);

                        if (row.child.isShown()) {
                            // This row is already open - close it
                            row.child.hide();
                            tr.removeClass('shown');
                        } else {
                            // Open this row
                            var data = tr.data('rounds');
                            row.child(formatMobileRow(data)).show();
                            tr.addClass('shown');

                            // Add click handler for the links inside the expanded row
                            tr.next('tr').find('a').on('click', function (e) {
                                e.stopPropagation(); // Prevent the row from collapsing
                            });
                        }
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching data:', error);
                }
            });
        }

        // Function to format the expanded row
        function formatMobileRow(data) {
            // Changed: Remove text-decoration, keep only color change
            const rowStyle = data.isHidden ? 'style="color: #999;"' : '';
            
            var roundsHtml = '<table class="child-table">';
            roundsHtml += '<tr>';
            data.roundsArray.forEach(function(round) {
                roundsHtml += `<td class="numeric-center" ${rowStyle}><b>R${round}</b></td>`;
            });
            roundsHtml += '</tr><tr>';
            data.roundsArray.forEach(function(round) {
                var roundScore = data.rounds[round] ? data.rounds[round] : '-';
                const linkColor = data.isHidden ? '#999' : 'black';
                roundsHtml += `<td class="numeric-center" ${rowStyle}><a href="result_details_round.php?event_id=<?php echo $event_id; ?>&formid=${data.id}&round=${round}" style="color: ${linkColor}; text-decoration: underline;">${roundScore}</a></td>`;
            });
            roundsHtml += '</tr></table>';
            return roundsHtml;
        }

        $(document).ready(function() {
            fetchAndUpdateData();

            $('#category_id').change(function() {
                fetchAndUpdateData();
            });
        });
    </script>
    <script>
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        if (menuToggle && mobileMenu) {
            menuToggle.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }
        // Animation to display the section
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.fade-in');
            const options = {
                threshold: 0.1
            };

            const observer = new IntersectionObserver(function(entries, observer) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, options);

            sections.forEach(section => {
                observer.observe(section);
            });
        });

        // Loading animation to make sure the content is well prepared
        window.addEventListener('load', function() {
            const loadingScreen = document.getElementById('loading-screen');
            const sections = document.querySelectorAll('.fade-in');
            const options = {
                threshold: 0.1
            };

            const observer = new IntersectionObserver(function(entries, observer) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, options);

            sections.forEach(section => {
                observer.observe(section);
            });

            // Hide loading screen when content is fully loaded
            if (loadingScreen) {
            loadingScreen.style.display = 'none';
            }
        });

        const menuToggle2 = document.getElementById('menu-toggle');
        const mobileMenu2 = document.getElementById('mobile-menu');
        if (menuToggle2 && mobileMenu2) {
            menuToggle2.addEventListener('click', () => {
                mobileMenu2.classList.toggle('hidden');
            });
        }
    </script>
    <script>
        // Add this function near the top of your JavaScript section
        function getPlayerStatus(hide_rmk) {
            switch(hide_rmk) {
                case 0: return 'WD';
                case 1: return 'DQ';
                case 2: return 'DNF';
                default: return '';
            }
        }

        // Add this function to create a status badge
        function createStatusBadge(status) {
            return `<span style="display: inline-block; padding: 2px 6px; font-size: 12px; border-radius: 3px; background-color: #ff6b6b; color: white; margin-left: 5px;">${status}</span>`;
        }
    </script>
</body>
</html>

