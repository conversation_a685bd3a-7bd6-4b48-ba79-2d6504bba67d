<?php
require("../database.php");

// Check admin session logic...
if (isset($_SESSION['profile_email'])) {
    $admin_name   = $_SESSION['profile_name'];
    $admin_roleid = $_SESSION['profile_role_id'];
    if ($admin_roleid != 'gPHOfKV0sL') {
        header("Location: ../index.php?error=accessdenied");
        exit;
    }
} else {
    header("Location: ../login.php?error=pagenotfound");
    exit;
}

// 1) Get the event_mgm_id and category_id
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
$category_id  = isset($_GET['category_id'])   ? $_GET['category_id'] : '';

// 2) Fetch event details
$event_query = "
    SELECT em.event_id,
           em.event_name,
           club_info.golf_club_name AS event_venue,
           em.event_start_date,
           em.event_end_date
    FROM event_mgm em
    JOIN club_info ON em.event_venue = club_info.club_id
    WHERE em.event_mgm_id = ?
";
$event_stmt = $conn->prepare($event_query);
$event_stmt->bind_param("s", $event_mgm_id);
$event_stmt->execute();
$event_result  = $event_stmt->get_result();
$event_details = $event_result->fetch_assoc();

// 3) Fetch categories for the event
$category_query = "
    SELECT DISTINCT ec.category_id, ec.category_name
    FROM event_mgm em
    JOIN event_category ec ON em.category_id = ec.category_id
    WHERE em.event_mgm_id = ?
";
$category_stmt = $conn->prepare($category_query);
$category_stmt->bind_param("s", $event_mgm_id);
$category_stmt->execute();
$category_result = $category_stmt->get_result();
$categories     = [];
while ($row = $category_result->fetch_assoc()) {
    $categories[] = $row;
}
$category_stmt->close();

// Determine actual $category_id
$category_id = $category_id ?: (count($categories) > 0 ? $categories[0]['category_id'] : '');

// 4) Fetch the event_id based on event_mgm_id and category_id
$event_id_query = "
    SELECT event_id
    FROM event_mgm
    WHERE event_mgm_id = ?
      AND category_id   = ?
";
$event_id_stmt = $conn->prepare($event_id_query);
$event_id_stmt->bind_param("ss", $event_mgm_id, $category_id);
$event_id_stmt->execute();
$event_id_result = $event_id_stmt->get_result();
$event_id_row    = $event_id_result->fetch_assoc();
$event_id        = $event_id_row['event_id'] ?? null;
$event_id_stmt->close();

// 5) Fetch hole data (optional if needed)
$holes_query = "
    SELECT ch.hole_number, ch.par
    FROM custom_hole ch
    JOIN event_category_tee_box ectb ON ch.custom_hole_id = ectb.custom_hole_id
    WHERE ectb.event_id = ?
    ORDER BY ch.hole_number ASC
";
$holes_stmt = $conn->prepare($holes_query);
$holes_stmt->bind_param("s", $event_id);
$holes_stmt->execute();
$holes_result = $holes_stmt->get_result();
$holes_data   = [];
while ($row = $holes_result->fetch_assoc()) {
    $holes_data[] = $row;
}
$holes_stmt->close();
$num_holes = count($holes_data);
$holes_json = json_encode($holes_data);

// 6) Fetch distinct rounds
$rounds_query = "
    SELECT DISTINCT s.round
    FROM scorecard s
    JOIN event_mgm em ON s.event_id = em.event_id
    WHERE em.event_mgm_id = ?
      AND em.category_id   = ?
    ORDER BY s.round
";
$rounds_stmt = $conn->prepare($rounds_query);
$rounds_stmt->bind_param("ss", $event_mgm_id, $category_id);
$rounds_stmt->execute();
$rounds_result = $rounds_stmt->get_result();
$rounds        = [];
while ($row = $rounds_result->fetch_assoc()) {
    $rounds[] = $row['round'];
}
$rounds_stmt->close();

// ---------------------------------------------------------------------
// 7) Determine MODE (GROSS, NETT, or SYSTEM36) and get scoring parameters
// ---------------------------------------------------------------------
$mode     = 'GROSS';
$max_score= 0;
$max_net  = 0;

$mode_sql = "SELECT mode FROM mode_of_game WHERE event_mgm_id = ? LIMIT 1";
$mStmt    = $conn->prepare($mode_sql);
$mStmt->bind_param("s", $event_mgm_id);
$mStmt->execute();
$mRes  = $mStmt->get_result();
if ($mRow = $mRes->fetch_assoc()) {
    $mode = $mRow['mode']; // 'GROSS', 'NETT', or 'SYSTEM36'
}
$mStmt->close();

if ($mode === 'NETT' || $mode === 'SYSTEM36' || $mode === 'STABLEFORD') {
    // Fetch both max_score and max_net for NETT, SYSTEM36, and STABLEFORD modes
    $nett_sql = "SELECT max_score, max_net FROM nett_format WHERE event_mgm_id = ? LIMIT 1";
    $nStmt    = $conn->prepare($nett_sql);
    $nStmt->bind_param("s", $event_mgm_id);
    $nStmt->execute();
    $nRes = $nStmt->get_result();
    if ($nRow = $nRes->fetch_assoc()) {
        $max_score = (int)$nRow['max_score'];
        $max_net   = (int)$nRow['max_net'];
    }
    $nStmt->close();
}

// System 36 point table based on nett score
function getSystem36Points($nettScore) {
    if ($nettScore <= 0) return 2;  // Par or better = 2 points
    if ($nettScore == 1) return 1;  // Bogey = 1 point
    return 0;                       // Double bogey or worse = 0 points
}

// Stableford point table based on nett score
function getStablefordPoints($nettScore) {
    if ($nettScore <= -4) {
        return 6; // -4 or better = 6 points
    } elseif ($nettScore == -3) {
        return 5; // -3 = 5 points  
    } elseif ($nettScore == -2) {
        return 4; // -2 = 4 points
    } elseif ($nettScore == -1) {
        return 3; // -1 = 3 points
    } elseif ($nettScore == 0) {
        return 2; // 0 = 2 points
    } elseif ($nettScore == 1) {
        return 1; // 1 = 1 point
    } else {
        return 0; // 2 or worse = 0 points
    }
}

// ---------------------------------------------------------------------
// 8) Build scoreboard => $player_scores
//     GROSS mode uses an aggregated query.
//     NETT mode does hole-by-hole capping and per–round calculations.
// ---------------------------------------------------------------------
$player_scores = [];
$latest_round  = !empty($rounds) ? max($rounds) : 1;

if ($mode === 'GROSS') {
    // -- 8A) GROSS mode aggregated query --
    $scores_query = "
        SELECT 
            s.form_id, 
            p.fullname, 
            p.nationality, 
            IFNULL(p.handicap, 0) AS handicap, 
            p.hide,
            p.hide_rmk,
            s.round, 
            SUM(IF(s.strokes > 0, s.strokes, 0)) AS total_strokes, 
            SUM(IF(s.strokes > 0, s.strokes - h.par, 0)) AS score,
            COALESCE(MAX(s.ocb), 0) as ocb
        FROM scorecard s
        JOIN registration_form p ON s.form_id = p.form_id
        JOIN custom_hole h       ON s.custom_hole_id = h.custom_hole_id
        JOIN event_mgm em        ON s.event_id = em.event_id
        WHERE em.event_mgm_id = ?
          AND em.category_id   = ?
        GROUP BY s.form_id, s.round
        ORDER BY score ASC
    ";
    $scores_stmt = $conn->prepare($scores_query);
    $scores_stmt->bind_param("ss", $event_mgm_id, $category_id);
    $scores_stmt->execute();
    $scores_result = $scores_stmt->get_result();

    while ($row = $scores_result->fetch_assoc()) {
        $player_id = $row['form_id'];
        $rnd       = $row['round'];
        if (!isset($player_scores[$player_id])) {
            $player_scores[$player_id] = [
                'id'            => $player_id,
                'name'          => $row['fullname'],
                'country'       => $row['nationality'],
                'handicap'      => (int)($row['handicap'] ?? 0),
                'hide'          => $row['hide'],
                'hide_rmk'      => $row['hide_rmk'],
                'total_strokes' => 0,
                'score'         => 0,
                'rounds'        => [],
                'ocb'           => 0
            ];
        }
        if (!isset($player_scores[$player_id]['rounds'][$rnd])) {
            $player_scores[$player_id]['rounds'][$rnd] = 0;
        }
        $player_scores[$player_id]['rounds'][$rnd] += (int)$row['total_strokes'];
        $player_scores[$player_id]['total_strokes'] += (int)$row['total_strokes'];
        $player_scores[$player_id]['score']         += (int)$row['score'];
        if ($rnd == $latest_round) {
            $player_scores[$player_id]['ocb'] = $row['ocb'];
        }
    }
    $scores_stmt->close();
} else if ($mode === 'NETT') {
    // --- 8B) NETT mode: hole-by-hole then per–round calculation ---
    $holesSql = "
    SELECT 
        s.form_id,
        p.fullname,
        p.nationality,
        IFNULL(p.handicap, 0) AS handicap,
        p.hide,
        p.hide_rmk,
        s.round,
        s.strokes,
        h.par,
        h.hole_number,
        COALESCE(s.ocb, 0) as ocb
    FROM scorecard s
    JOIN registration_form p ON s.form_id = p.form_id
    JOIN custom_hole h ON s.custom_hole_id = h.custom_hole_id
    JOIN event_mgm em ON s.event_id = em.event_id
    WHERE em.event_mgm_id = ?
      AND em.category_id = ?
    ORDER BY s.form_id, s.round, h.hole_number
    ";
    $hStmt = $conn->prepare($holesSql);
    $hStmt->bind_param("ss", $event_mgm_id, $category_id);
    $hStmt->execute();
    $hRes = $hStmt->get_result();

    $rawData = []; // Group records per player and round.
    while ($row = $hRes->fetch_assoc()) {
        $fid = $row['form_id'];
        $roundNo = (int)$row['round'];
        if (!isset($rawData[$fid])) {
            $rawData[$fid] = [
                'fullname'    => $row['fullname'],
                'nationality' => $row['nationality'],
                'handicap'    => (int)$row['handicap'],
                'hide'        => (int)$row['hide'],
                'hide_rmk'    => (int)$row['hide_rmk'],
                'rounds'      => [] // each round is an array of hole records.
            ];
        }
        if (!isset($rawData[$fid]['rounds'][$roundNo])) {
            $rawData[$fid]['rounds'][$roundNo] = [];
        }
        $rawData[$fid]['rounds'][$roundNo][] = [
            'strokes'     => (int)$row['strokes'],
            'par'         => (int)$row['par'],
            'hole_number' => (int)$row['hole_number'],
            'ocb'         => (int)$row['ocb']
        ];
    }
    $hStmt->close();

    // Process the grouped data to compute per-round and overall totals.
    foreach ($rawData as $fid => $pObj) {
        if (!isset($player_scores[$fid])) {
            $player_scores[$fid] = [
                'id'             => $fid,
                'name'           => $pObj['fullname'],
                'country'        => $pObj['nationality'],
                'handicap'       => $pObj['handicap'],
                'hide'           => $pObj['hide'],
                'hide_rmk'       => $pObj['hide_rmk'],
                'total_strokes'  => 0,
                'score'          => 0,   // gross: sum of (strokes - par) over rounds
                'nett'           => 0,   // overall NETT (sum of round_nett values)
                'rounds'         => [],
                'rounds_nett'    => [],
                'ocb'           => 0,
                'hole_display'   => '-'
            ];
        }
        foreach ($pObj['rounds'] as $rNo => $holesArr) {
            $roundStrokes = 0;
            $roundPar = 0;
            $holesPlayed = 0;
            $round_ocb_sum = 0;
            $round_ocb_count = 0;

            foreach ($holesArr as $hInfo) {
                $st   = (int)$hInfo['strokes'];
                $par  = (int)$hInfo['par'];
                $ocbV = (int)$hInfo['ocb'];
            
                // Only process if the hole is actually played
                if ($st > 0) {
                    // Apply maximum stroke cap if max_score > 0
                    if ($max_score > 0) {
                        $cap = $par + $max_score;
                        if ($st > $cap) {
                            $st = $cap;
                        }
                    }
                    $holesPlayed++;
                    $roundStrokes += $st;
                    $roundPar     += $par;
            
                    $round_ocb_sum   += $ocbV;
                    $round_ocb_count++;
                }
            }

            // calculate average ocb for the round
            $round_ocb = $round_ocb_count > 0 ? $round_ocb_sum / $round_ocb_count : 0;

            $player_scores[$fid]['rounds'][$rNo] = $roundStrokes;
            $player_scores[$fid]['total_strokes'] += $roundStrokes;
            $player_scores[$fid]['score'] += ($roundStrokes - $roundPar);
            
            // --- Calculate ROUND NETT ---
            $raw_round_nett = $roundStrokes - $player_scores[$fid]['handicap'];
            $min_round_nett = $roundPar - $max_net;
            $round_nett = ($raw_round_nett < $min_round_nett) ? $min_round_nett : $raw_round_nett;
            $player_scores[$fid]['rounds_nett'][$rNo] = $round_nett;
            $player_scores[$fid]['nett'] += $round_nett;
            $player_scores[$fid]['ocb'] = $round_ocb;

            if ($rNo == $latest_round) {
                $player_scores[$fid]['hole_display'] = ($holesPlayed >= 18) ? 'F' : ($holesPlayed > 0 ? $holesPlayed : '-');
            }
        }
    }
} else if ($mode === 'SYSTEM36') {
    // --- 8C) SYSTEM36 mode: hole-by-hole, calculate nett scores and convert to points ---
    $holesSql = "
    SELECT 
        s.form_id,
        p.fullname,
        p.nationality,
        IFNULL(p.handicap, 0) AS handicap,
        p.hide,
        p.hide_rmk,
        s.round,
        s.strokes,
        h.par,
        h.hole_number,
        COALESCE(s.ocb, 0) as ocb
    FROM scorecard s
    JOIN registration_form p ON s.form_id = p.form_id
    JOIN custom_hole h ON s.custom_hole_id = h.custom_hole_id
    JOIN event_mgm em ON s.event_id = em.event_id
    WHERE em.event_mgm_id = ?
      AND em.category_id = ?
    ORDER BY s.form_id, s.round, h.hole_number
    ";
    $hStmt = $conn->prepare($holesSql);
    $hStmt->bind_param("ss", $event_mgm_id, $category_id);
    $hStmt->execute();
    $hRes = $hStmt->get_result();

    $rawData = []; // Group records per player and round.
    while ($row = $hRes->fetch_assoc()) {
        $fid = $row['form_id'];
        $roundNo = (int)$row['round'];
        if (!isset($rawData[$fid])) {
            $rawData[$fid] = [
                'fullname'    => $row['fullname'],
                'nationality' => $row['nationality'],
                'handicap'    => (int)$row['handicap'],
                'hide'        => (int)$row['hide'],
                'hide_rmk'    => (int)$row['hide_rmk'],
                'rounds'      => [] // each round is an array of hole records.
            ];
        }
        if (!isset($rawData[$fid]['rounds'][$roundNo])) {
            $rawData[$fid]['rounds'][$roundNo] = [];
        }
        $rawData[$fid]['rounds'][$roundNo][] = [
            'strokes'     => (int)$row['strokes'],
            'par'         => (int)$row['par'],
            'hole_number' => (int)$row['hole_number'],
            'ocb'         => (int)$row['ocb']
        ];
    }
    $hStmt->close();

    // Process the grouped data to compute System 36 scores
    foreach ($rawData as $fid => $pObj) {
        if (!isset($player_scores[$fid])) {
            $player_scores[$fid] = [
                'id'             => $fid,
                'name'           => $pObj['fullname'],
                'country'        => $pObj['nationality'],
                'handicap'       => $pObj['handicap'],
                'hide'           => $pObj['hide'],
                'hide_rmk'       => $pObj['hide_rmk'],
                'total_strokes'  => 0,
                'score'          => 0,   // gross: sum of (strokes - par) over rounds
                'nett'           => 0,   // overall NETT (sum of round_nett values)
                'system36_points'=> 0,   // total System 36 points
                'hcp_of_day'     => 0,   // HCP of the day (36 - total points)
                'rounds'         => [],
                'rounds_nett'    => [],
                'rounds_points'  => [],
                'ocb'           => 0,
                'hole_display'   => '-'
            ];
        }
        
        // System 36 doesn't use handicap strokes per hole
        // Points are calculated directly from (strokes - par)
        
        foreach ($pObj['rounds'] as $rNo => $holesArr) {
            $roundStrokes = 0;
            $roundPar = 0;
            $roundPoints = 0;
            $holesPlayed = 0;
            $round_ocb_sum = 0;
            $round_ocb_count = 0;

            foreach ($holesArr as $hInfo) {
                $st   = (int)$hInfo['strokes'];
                $par  = (int)$hInfo['par'];
                $holeNum = (int)$hInfo['hole_number'];
                $ocbV = (int)$hInfo['ocb'];
            
                // Only process if the hole is actually played
                if ($st > 0) {
                    // Apply maximum stroke cap if max_score > 0
                    if ($max_score > 0) {
                        $cap = $par + $max_score;
                        if ($st > $cap) {
                            $st = $cap;
                        }
                    }
                    
                    // Calculate nett score for System 36: Result - Par
                    $nettScore = $st - $par;
                    
                    // Convert nett score to System 36 points using S36 Point table
                    $points = getSystem36Points($nettScore);
                    $roundPoints += $points;
                    
                    $holesPlayed++;
                    $roundStrokes += $st;
                    $roundPar     += $par;
            
                    $round_ocb_sum   += $ocbV;
                    $round_ocb_count++;
                }
            }

            // calculate average ocb for the round
            $round_ocb = $round_ocb_count > 0 ? $round_ocb_sum / $round_ocb_count : 0;

            $player_scores[$fid]['rounds'][$rNo] = $roundStrokes;
            $player_scores[$fid]['rounds_points'][$rNo] = $roundPoints;
            $player_scores[$fid]['total_strokes'] += $roundStrokes;
            $player_scores[$fid]['score'] += ($roundStrokes - $roundPar);
            $player_scores[$fid]['system36_points'] += $roundPoints;
            $player_scores[$fid]['ocb'] = $round_ocb;

            if ($rNo == $latest_round) {
                $player_scores[$fid]['hole_display'] = ($holesPlayed >= 18) ? 'F' : ($holesPlayed > 0 ? $holesPlayed : '-');
            }
        }
        
        // Calculate HCP of the day (36 × rounds - total points)
        $totalRounds = count($pObj['rounds']);
        $maxPossiblePoints = 36 * $totalRounds; // 36 points per round (18 holes × 2 points each)
        $player_scores[$fid]['hcp_of_day'] = $maxPossiblePoints - $player_scores[$fid]['system36_points'];
    }
} else if ($mode === 'STABLEFORD') {
    // --- 8D) STABLEFORD mode: hole-by-hole, calculate nett scores and convert to Stableford points ---
    $holesSql = "
    SELECT 
        s.form_id,
        p.fullname,
        p.nationality,
        IFNULL(p.handicap, 0) AS handicap,
        p.hide,
        p.hide_rmk,
        s.round,
        s.strokes,
        h.par,
        h.hole_number,
        h.golf_index,
        COALESCE(s.ocb, 0) as ocb
    FROM scorecard s
    JOIN registration_form p ON s.form_id = p.form_id
    JOIN custom_hole h ON s.custom_hole_id = h.custom_hole_id
    JOIN event_mgm em ON s.event_id = em.event_id
    WHERE em.event_mgm_id = ?
      AND em.category_id = ?
    ORDER BY s.form_id, s.round, h.hole_number
    ";
    $hStmt = $conn->prepare($holesSql);
    $hStmt->bind_param("ss", $event_mgm_id, $category_id);
    $hStmt->execute();
    $hRes = $hStmt->get_result();

    $rawData = []; // Group records per player and round.
    while ($row = $hRes->fetch_assoc()) {
        $fid = $row['form_id'];
        $roundNo = (int)$row['round'];
        if (!isset($rawData[$fid])) {
            $rawData[$fid] = [
                'fullname'    => $row['fullname'],
                'nationality' => $row['nationality'],
                'handicap'    => (int)$row['handicap'],
                'hide'        => (int)$row['hide'],
                'hide_rmk'    => (int)$row['hide_rmk'],
                'rounds'      => [] // each round is an array of hole records.
            ];
        }
        if (!isset($rawData[$fid]['rounds'][$roundNo])) {
            $rawData[$fid]['rounds'][$roundNo] = [];
        }
        $rawData[$fid]['rounds'][$roundNo][] = [
            'strokes'     => (int)$row['strokes'],
            'par'         => (int)$row['par'],
            'hole_number' => (int)$row['hole_number'],
            'golf_index'  => (int)$row['golf_index'],
            'ocb'         => (int)$row['ocb']
        ];
    }
    $hStmt->close();

    // Two-pass approach for Stableford: first calculate System 36 points, then use HCP of Day for final Stableford
    foreach ($rawData as $fid => $pObj) {
        if (!isset($player_scores[$fid])) {
            $player_scores[$fid] = [
                'id'             => $fid,
                'name'           => $pObj['fullname'],
                'country'        => $pObj['nationality'],
                'handicap'       => $pObj['handicap'],
                'hide'           => $pObj['hide'],
                'hide_rmk'       => $pObj['hide_rmk'],
                'total_strokes'  => 0,
                'score'          => 0,   // gross: sum of (strokes - par) over rounds
                'stableford_points' => 0, // total Stableford points
                'hcp_of_day'     => 0,   // HCP of the day (calculated from System 36)
                'rounds'         => [],
                'rounds_points'  => [],
                'ocb'           => 0,
                'hole_display'   => '-'
            ];
        }

        // First pass: Calculate System 36 points to determine HCP of Day
        $total_system36_points = 0;
        $total_holes_played = 0;
        
        foreach ($pObj['rounds'] as $rNo => $holesArr) {
            foreach ($holesArr as $hInfo) {
                $st   = (int)$hInfo['strokes'];
                $par  = (int)$hInfo['par'];
                
                if ($st > 0) {
                    // Apply maximum stroke cap if max_score > 0
                    if ($max_score > 0) {
                        $cap = $par + $max_score;
                        if ($st > $cap) {
                            $st = $cap;
                        }
                    }
                    
                    // Calculate nett score for System 36: Result - Par (no handicap applied)
                    $system36_nett = $st - $par;
                    
                    // Convert to System 36 points
                    $system36_points = getSystem36Points($system36_nett);
                    $total_system36_points += $system36_points;
                    $total_holes_played++;
                }
            }
        }
        
        // Calculate HCP of Day from System 36 points
        $player_scores[$fid]['hcp_of_day'] = ($total_holes_played * 2) - $total_system36_points;
        
        // Second pass: Calculate actual Stableford points using HCP of Day
        foreach ($pObj['rounds'] as $rNo => $holesArr) {
            $roundStrokes = 0;
            $roundPar = 0;
            $roundPoints = 0;
            $holesPlayed = 0;
            $round_ocb_sum = 0;
            $round_ocb_count = 0;

            foreach ($holesArr as $hInfo) {
                $st   = (int)$hInfo['strokes'];
                $par  = (int)$hInfo['par'];
                $golfIndex = (int)$hInfo['golf_index'];
                $ocbV = (int)$hInfo['ocb'];
                
                if ($st > 0) {
                    // Apply maximum stroke cap if max_score > 0
                    if ($max_score > 0) {
                        $cap = $par + $max_score;
                        if ($st > $cap) {
                            $st = $cap;
                        }
                    }
                    
                    // Calculate handicap strokes for this hole using HCP of Day
                    $handicapStrokes = 0;
                    if ($player_scores[$fid]['hcp_of_day'] >= $golfIndex) {
                        $handicapStrokes = 1;
                        if ($player_scores[$fid]['hcp_of_day'] >= ($golfIndex + 18)) {
                            $handicapStrokes = 2;
                        }
                    }
                    
                    // Calculate net score: Strokes - Handicap Strokes - Par
                    $nettScore = $st - $handicapStrokes - $par;
                    
                    // Convert nett score to Stableford points
                    $points = getStablefordPoints($nettScore);
                    $roundPoints += $points;
                    
                    $holesPlayed++;
                    $roundStrokes += $st;
                    $roundPar     += $par;
            
                    $round_ocb_sum   += $ocbV;
                    $round_ocb_count++;
                }
            }

            // calculate average ocb for the round
            $round_ocb = $round_ocb_count > 0 ? $round_ocb_sum / $round_ocb_count : 0;

            $player_scores[$fid]['rounds'][$rNo] = $roundStrokes;
            $player_scores[$fid]['rounds_points'][$rNo] = $roundPoints;
            $player_scores[$fid]['total_strokes'] += $roundStrokes;
            $player_scores[$fid]['score'] += ($roundStrokes - $roundPar);
            $player_scores[$fid]['stableford_points'] += $roundPoints;
            $player_scores[$fid]['ocb'] = $round_ocb;

            if ($rNo == $latest_round) {
                $player_scores[$fid]['hole_display'] = ($holesPlayed >= 18) ? 'F' : ($holesPlayed > 0 ? $holesPlayed : '-');
            }
        }
    }
}

// ---------------------------------------------------------------------
// 9) Sort and Tie–Handling
// ---------------------------------------------------------------------
$player_scores = array_values($player_scores);

// Sort players based on mode
usort($player_scores, function($a, $b) use ($mode, $conn, $event_mgm_id, $latest_round) {
    // First check hide status - hide=1 players always go to bottom
    if ($a['hide'] != $b['hide']) {
        return $a['hide'] <=> $b['hide'];
    }

    // If both are hidden, sort by hide_rmk
    if ($a['hide'] && $b['hide']) {
        return $a['hide_rmk'] <=> $b['hide_rmk'];
    }

    // Check if players have started the current round
    $a_started_current = isset($a['rounds'][$latest_round]) && $a['rounds'][$latest_round] > 0;
    $b_started_current = isset($b['rounds'][$latest_round]) && $b['rounds'][$latest_round] > 0;
    
    // If one has started the current round and the other hasn't, prioritize the one who started
    if ($a_started_current !== $b_started_current) {
        return $b_started_current <=> $a_started_current; // Reverse comparison to put started players first
    }
    
    // If neither has started the current round, check if they have any scores at all
    if (!$a_started_current && !$b_started_current) {
        $a_has_any_scores = $a['total_strokes'] > 0;
        $b_has_any_scores = $b['total_strokes'] > 0;
        
        // If one has any scores and the other doesn't, prioritize the one with scores
        if ($a_has_any_scores !== $b_has_any_scores) {
            return $b_has_any_scores <=> $a_has_any_scores; // Reverse comparison to put players with scores first
        }
    }

    if ($mode === 'NETT') {
        // push total_strokes=0 to bottom if both are 0 or one is 0
        if ($a['total_strokes'] == 0 && $b['total_strokes'] == 0) return 0;
        if ($a['total_strokes'] == 0) return 1;
        if ($b['total_strokes'] == 0) return -1;

        // compare NETT
        if ($a['nett'] === $b['nett']) {
            // Check if OCB is enabled for this event
            $check_ocb = "SELECT apply_ocb FROM ocb_rules_apply WHERE event_mgm_id = ? AND apply_ocb = 1";
            $ocb_stmt = $conn->prepare($check_ocb);
            $ocb_stmt->bind_param("s", $event_mgm_id);
            $ocb_stmt->execute();
            $ocb_result = $ocb_stmt->get_result();

            // If OCB is not enabled, sort by name
            if ($ocb_result->num_rows === 0 || $ocb_result->fetch_assoc()['apply_ocb'] != 1) {
                return strcmp($a['name'], $b['name']);
            }

            // Get OCB rules in priority order
            $rules_query = "SELECT condition_type, condition_value 
                           FROM ocb_rules 
                           WHERE event_mgm_id = ? 
                           ORDER BY priority_order ASC";
            $rules_stmt = $conn->prepare($rules_query);
            $rules_stmt->bind_param("s", $event_mgm_id);
            $rules_stmt->execute();
            $rules = $rules_stmt->get_result()->fetch_all(MYSQLI_ASSOC);

            // Try each rule until we find one that breaks the tie
            foreach ($rules as $rule) {
                $a_score = getOCBScoreForRule($conn, $a['id'], $event_mgm_id, $rule);
                $b_score = getOCBScoreForRule($conn, $b['id'], $event_mgm_id, $rule);

                if ($a_score !== $b_score) {
                    return $a_score <=> $b_score;
                }
            }

            // If all rules are exhausted, sort by name
            return strcmp($a['name'], $b['name']);
        }
        return ($a['nett'] < $b['nett']) ? -1 : 1;
    } else if ($mode === 'SYSTEM36') {
        // push total_strokes=0 to bottom if both are 0 or one is 0
        if ($a['total_strokes'] == 0 && $b['total_strokes'] == 0) return 0;
        if ($a['total_strokes'] == 0) return 1;
        if ($b['total_strokes'] == 0) return -1;

        // compare HCP of the day (lower HCP is better)
        if ($a['hcp_of_day'] === $b['hcp_of_day']) {
            // Check if OCB is enabled for this event
            $check_ocb = "SELECT apply_ocb FROM ocb_rules_apply WHERE event_mgm_id = ? AND apply_ocb = 1";
            $ocb_stmt = $conn->prepare($check_ocb);
            $ocb_stmt->bind_param("s", $event_mgm_id);
            $ocb_stmt->execute();
            $ocb_result = $ocb_stmt->get_result();

            // If OCB is not enabled, sort by name
            if ($ocb_result->num_rows === 0 || $ocb_result->fetch_assoc()['apply_ocb'] != 1) {
                return strcmp($a['name'], $b['name']);
            }

            // Get OCB rules in priority order
            $rules_query = "SELECT condition_type, condition_value 
                           FROM ocb_rules 
                           WHERE event_mgm_id = ? 
                           ORDER BY priority_order ASC";
            $rules_stmt = $conn->prepare($rules_query);
            $rules_stmt->bind_param("s", $event_mgm_id);
            $rules_stmt->execute();
            $rules = $rules_stmt->get_result()->fetch_all(MYSQLI_ASSOC);

            // Try each rule until we find one that breaks the tie
            foreach ($rules as $rule) {
                $a_score = getOCBScoreForRule($conn, $a['id'], $event_mgm_id, $rule);
                $b_score = getOCBScoreForRule($conn, $b['id'], $event_mgm_id, $rule);

                if ($a_score !== $b_score) {
                    return $a_score <=> $b_score;
                }
            }

            // If all rules are exhausted, sort by name
            return strcmp($a['name'], $b['name']);
        }
        return ($a['hcp_of_day'] < $b['hcp_of_day']) ? -1 : 1;
    } else if ($mode === 'STABLEFORD') {
        // push total_strokes=0 to bottom if both are 0 or one is 0
        if ($a['total_strokes'] == 0 && $b['total_strokes'] == 0) return 0;
        if ($a['total_strokes'] == 0) return 1;
        if ($b['total_strokes'] == 0) return -1;

        // compare Stableford points (higher is better)
        if ($a['stableford_points'] === $b['stableford_points']) {
            // Check if OCB is enabled for this event
            $check_ocb = "SELECT apply_ocb FROM ocb_rules_apply WHERE event_mgm_id = ? AND apply_ocb = 1";
            $ocb_stmt = $conn->prepare($check_ocb);
            $ocb_stmt->bind_param("s", $event_mgm_id);
            $ocb_stmt->execute();
            $ocb_result = $ocb_stmt->get_result();

            // If OCB is not enabled, sort by name
            if ($ocb_result->num_rows === 0 || $ocb_result->fetch_assoc()['apply_ocb'] != 1) {
                return strcmp($a['name'], $b['name']);
            }

            // Get OCB rules in priority order
            $rules_query = "SELECT condition_type, condition_value 
                           FROM ocb_rules 
                           WHERE event_mgm_id = ? 
                           ORDER BY priority_order ASC";
            $rules_stmt = $conn->prepare($rules_query);
            $rules_stmt->bind_param("s", $event_mgm_id);
            $rules_stmt->execute();
            $rules = $rules_stmt->get_result()->fetch_all(MYSQLI_ASSOC);

            // Try each rule until we find one that breaks the tie
            foreach ($rules as $rule) {
                $a_score = getOCBScoreForRule($conn, $a['id'], $event_mgm_id, $rule);
                $b_score = getOCBScoreForRule($conn, $b['id'], $event_mgm_id, $rule);

                if ($a_score !== $b_score) {
                    return $a_score <=> $b_score;
                }
            }

            // If all rules are exhausted, sort by name
            return strcmp($a['name'], $b['name']);
        }
        return ($a['stableford_points'] > $b['stableford_points']) ? -1 : 1; // Higher points = better position
    } else {
        // push total_strokes=0 to bottom
        if ($a['total_strokes'] == 0 && $b['total_strokes'] == 0) return 0;
        if ($a['total_strokes'] == 0) return 1;
        if ($b['total_strokes'] == 0) return -1;

        // compare score
        if ($a['score'] == $b['score']) {
            // compare strokes
            if ($a['total_strokes'] == $b['total_strokes']) {
                // Check if OCB is enabled for this event
                $check_ocb = "SELECT apply_ocb FROM ocb_rules_apply WHERE event_mgm_id = ? AND apply_ocb = 1";
                $ocb_stmt = $conn->prepare($check_ocb);
                $ocb_stmt->bind_param("s", $event_mgm_id);
                $ocb_stmt->execute();
                $ocb_result = $ocb_stmt->get_result();

                // If OCB is not enabled, sort by name
                if ($ocb_result->num_rows === 0 || $ocb_result->fetch_assoc()['apply_ocb'] != 1) {
                    return strcmp($a['name'], $b['name']);
                }

                // Get OCB rules in priority order
                $rules_query = "SELECT condition_type, condition_value 
                               FROM ocb_rules 
                               WHERE event_mgm_id = ? 
                               ORDER BY priority_order ASC";
                $rules_stmt = $conn->prepare($rules_query);
                $rules_stmt->bind_param("s", $event_mgm_id);
                $rules_stmt->execute();
                $rules = $rules_stmt->get_result()->fetch_all(MYSQLI_ASSOC);

                // Try each rule until we find one that breaks the tie
                foreach ($rules as $rule) {
                    $a_score = getOCBScoreForRule($conn, $a['id'], $event_mgm_id, $rule);
                    $b_score = getOCBScoreForRule($conn, $b['id'], $event_mgm_id, $rule);

                    if ($a_score !== $b_score) {
                        return $a_score <=> $b_score;
                    }
                }

                // If all rules are exhausted, sort by name
                return strcmp($a['name'], $b['name']);
            }
            return $a['total_strokes'] <=> $b['total_strokes'];
        }
        return $a['score'] <=> $b['score'];
    }
});

$position = 1;
$previous_score = null;
$previous_strokes = null;
$previous_nett = null; // track previous nett score
$previous_hcp_of_day = null; // track previous HCP of the day for System 36
$previous_stableford_points = null; // track previous Stableford points
$positions = [];
foreach ($player_scores as $i => &$plr) {
    if ($mode === 'NETT' && $previous_nett !== null && $plr['nett'] === $previous_nett) {
         $plr['position'] = $positions[$i - 1];
    }
    elseif ($mode === 'SYSTEM36' && $previous_hcp_of_day !== null && $plr['hcp_of_day'] === $previous_hcp_of_day) {
         $plr['position'] = $positions[$i - 1];
    }
    elseif ($mode === 'STABLEFORD' && $previous_stableford_points !== null && $plr['stableford_points'] === $previous_stableford_points) {
         $plr['position'] = $positions[$i - 1];
    }
    elseif ($mode === 'GROSS' && $previous_score !== null && $previous_strokes !== null &&
        $plr['score'] === $previous_score && $plr['total_strokes'] === $previous_strokes) {
        $plr['position'] = $positions[$i - 1];
    } else {
        $plr['position'] = (string)$position;
    }
    $positions[] = $plr['position'];
    $position++;

    if ($mode === 'NETT') {
        $previous_nett = $plr['nett'];
    } elseif ($mode === 'SYSTEM36') {
        $previous_hcp_of_day = $plr['hcp_of_day'];
    } elseif ($mode === 'STABLEFORD') {
        $previous_stableford_points = $plr['stableford_points'];
    } else{
        $previous_score = $plr['score'];
        $previous_strokes = $plr['total_strokes'];
    }
}
unset($plr);

for ($i = 0; $i < count($player_scores) - 1; $i++) {
    if ($mode === 'NETT' && $player_scores[$i]['nett'] == $player_scores[$i + 1]['nett']) {
        if (strpos($player_scores[$i]['position'], 'T') !== 0) {
            $player_scores[$i]['position'] = 'T' . $player_scores[$i]['position'];
        }
        if (strpos($player_scores[$i + 1]['position'], 'T') !== 0) {
            $player_scores[$i + 1]['position'] = 'T' . $player_scores[$i + 1]['position'];
        }
    } elseif ($mode === 'SYSTEM36' && $player_scores[$i]['hcp_of_day'] == $player_scores[$i + 1]['hcp_of_day']) {
        if (strpos($player_scores[$i]['position'], 'T') !== 0) {
            $player_scores[$i]['position'] = 'T' . $player_scores[$i]['position'];
        }
        if (strpos($player_scores[$i + 1]['position'], 'T') !== 0) {
            $player_scores[$i + 1]['position'] = 'T' . $player_scores[$i + 1]['position'];
        }
    } elseif ($mode === 'STABLEFORD' && $player_scores[$i]['stableford_points'] == $player_scores[$i + 1]['stableford_points']) {
        if (strpos($player_scores[$i]['position'], 'T') !== 0) {
            $player_scores[$i]['position'] = 'T' . $player_scores[$i]['position'];
        }
        if (strpos($player_scores[$i + 1]['position'], 'T') !== 0) {
            $player_scores[$i + 1]['position'] = 'T' . $player_scores[$i + 1]['position'];
        }
    } elseif($mode === 'GROSS' && $player_scores[$i]['score'] == $player_scores[$i + 1]['score'] &&
        $player_scores[$i]['total_strokes'] == $player_scores[$i + 1]['total_strokes']) {
        if (strpos($player_scores[$i]['position'], 'T') !== 0) {
            $player_scores[$i]['position'] = 'T' . $player_scores[$i]['position'];
        }
        if (strpos($player_scores[$i + 1]['position'], 'T') !== 0) {
            $player_scores[$i + 1]['position'] = 'T' . $player_scores[$i + 1]['position'];
        }
    }
}

/***********************************************************************
 * 4B) SUDDEN DEATH SORTING FOR T1 POSITIONS
 ***********************************************************************/
// Find players with T1 position
$t1_players = array_filter($player_scores, function($player) {
    return $player['position'] === 'T1';
});

if (count($t1_players) > 1) {
    // Get sudden death scores for T1 players
    $t1_form_ids = array_column($t1_players, 'id');
    $form_ids_str = str_repeat('?,', count($t1_form_ids) - 1) . '?';
    
    $sudden_death_query = "
        SELECT form_id, SUM(strokes) as sudden_death_total
        FROM sudden_death_score
        WHERE form_id IN ($form_ids_str)
        AND event_id = (
            SELECT event_id 
            FROM event_mgm 
            WHERE event_mgm_id = ? 
            AND category_id = ?
        )
        GROUP BY form_id";

    $stmt = $conn->prepare($sudden_death_query);
    
    // Create parameter array for binding
    $params = array_merge($t1_form_ids, [$event_mgm_id, $category_id]);
    $types = str_repeat('s', count($t1_form_ids)) . 'ss';
    $stmt->bind_param($types, ...$params);
    
    $stmt->execute();
    $sudden_death_results = $stmt->get_result();
    $sudden_death_scores = [];
    
    while ($row = $sudden_death_results->fetch_assoc()) {
        $sudden_death_scores[$row['form_id']] = (int)$row['sudden_death_total'];
    }
    $stmt->close();

    // Sort T1 players based on mode and sudden death scores
    usort($t1_players, function($a, $b) use ($mode, $sudden_death_scores) {
        $a_sudden = isset($sudden_death_scores[$a['id']]) ? $sudden_death_scores[$a['id']] : PHP_INT_MAX;
        $b_sudden = isset($sudden_death_scores[$b['id']]) ? $sudden_death_scores[$b['id']] : PHP_INT_MAX;
        
        if ($mode === 'NETT') {
            // For NETT mode, add sudden death to nett score
            $a_total = $a['nett'] + $a_sudden;
            $b_total = $b['nett'] + $b_sudden;
        } elseif ($mode === 'STABLEFORD') {
            // For STABLEFORD mode, subtract sudden death from points (lower score is better in sudden death)
            $a_total = $a['stableford_points'] - $a_sudden;
            $b_total = $b['stableford_points'] - $b_sudden;
            return $b_total <=> $a_total; // Higher adjusted points win
        } else {
            // For GROSS mode, add sudden death to total strokes
            $a_total = $a['total_strokes'] + $a_sudden;
            $b_total = $b['total_strokes'] + $b_sudden;
        }
        
        return $a_total <=> $b_total;
    });

    // Update the main array with the new T1 order
    foreach ($player_scores as $key => $player) {
        if ($player['position'] === 'T1') {
            // Remove this T1 player
            unset($player_scores[$key]);
        }
    }
    
    // Add sorted T1 players back at the beginning
    $player_scores = array_merge($t1_players, array_values($player_scores));
}

// Add helper functions from fetch_data.php
function getOCBScoreForRule($conn, $form_id, $event_mgm_id, $rule) {
    // Get player's latest round
    $round_query = "SELECT MAX(round) as latest_round FROM scorecard s WHERE s.form_id = ?";
    $round_stmt = $conn->prepare($round_query);
    $round_stmt->bind_param("s", $form_id);
    $round_stmt->execute();
    $latest_round = $round_stmt->get_result()->fetch_assoc()['latest_round'];

    switch ($rule['condition_type']) {
        case 'last_x_holes':
            $holes_query = "SELECT s.strokes 
                          FROM scorecard s 
                          JOIN custom_hole h ON s.custom_hole_id = h.custom_hole_id 
                          WHERE s.form_id = ? 
                          AND s.round = ? 
                          AND h.hole_number > (18 - ?)  -- This ensures we get holes 10-18 for last 9
                          ORDER BY h.hole_number ASC";
            $holes_stmt = $conn->prepare($holes_query);
            $x_holes = (int)$rule['condition_value'];
            $holes_stmt->bind_param("sii", $form_id, $latest_round, $x_holes);
            $holes_stmt->execute();
            $holes_result = $holes_stmt->get_result();
            
            $score = 0;
            while ($hole = $holes_result->fetch_assoc()) {
                $score += (int)$hole['strokes'];
            }
            return $score;

        case 'second_last_round':
            $second_last_round = $latest_round - 1;
            if ($second_last_round > 0) {
                $round_query = "SELECT SUM(strokes) as total 
                              FROM scorecard 
                              WHERE form_id = ? AND round = ?";
                $round_stmt = $conn->prepare($round_query);
                $round_stmt->bind_param("si", $form_id, $second_last_round);
                $round_stmt->execute();
                $result = $round_stmt->get_result()->fetch_assoc();
                return $result['total'] ?? PHP_INT_MAX;
            }
            return PHP_INT_MAX;

        case 'handicap':
            $handicap_query = "SELECT handicap FROM registration_form WHERE form_id = ?";
            $handicap_stmt = $conn->prepare($handicap_query);
            $handicap_stmt->bind_param("s", $form_id);
            $handicap_stmt->execute();
            $handicap_result = $handicap_stmt->get_result();
            $handicap = (int)$handicap_result->fetch_assoc()['handicap'];
            return ($rule['condition_value'] === 'lowest') ? $handicap : -$handicap;

        case 'random_draw':
            return rand(1, 1000);
    }
    
    return PHP_INT_MAX;
}

// ---------------------------------------------------------------------
// 10) Fetch logos, sponsor logos, category IDs, etc.
// ---------------------------------------------------------------------
$logo_query = "SELECT id, logo FROM live_logo WHERE event_mgm_id = ?";
$logo_stmt  = $conn->prepare($logo_query);
$logo_stmt->bind_param("s", $event_mgm_id);
$logo_stmt->execute();
$logo_result = $logo_stmt->get_result();
$logos = [];
while ($logo_row = $logo_result->fetch_assoc()) {
    $logos[] = $logo_row;
}
$logo_stmt->close();

// Fetch sponsor logos for the event
$sponsor_logos = [];
$sponsor_logos_query = "SELECT id, logo FROM sponsor_logo WHERE event_mgm_id = ?";
$sponsor_logos_stmt = $conn->prepare($sponsor_logos_query);
$sponsor_logos_stmt->bind_param("s", $event_mgm_id);
$sponsor_logos_stmt->execute();
$sponsor_logos_result = $sponsor_logos_stmt->get_result();
while ($sponsor_logo_row = $sponsor_logos_result->fetch_assoc()) {
    $sponsor_logos[] = $sponsor_logo_row;
}
$sponsor_logos_stmt->close();

$cat_query = "
    SELECT DISTINCT category_id 
    FROM event_mgm 
    WHERE event_mgm_id = ?
";
$cat_stmt = $conn->prepare($cat_query);
$cat_stmt->bind_param("s", $event_mgm_id);
$cat_stmt->execute();
$cat_result = $cat_stmt->get_result();
$category_ids = [];
while ($cat_row = $cat_result->fetch_assoc()) {
    $category_ids[] = $cat_row['category_id'];
}
$cat_stmt->close();

$download_pdf_url   = "download_results.php?event_mgm_id=" . urlencode($event_mgm_id);
$download_excel_url = "export_results_excel.php?event_mgm_id=" . urlencode($event_mgm_id);
foreach ($category_ids as $cid) {
    $download_pdf_url   .= "&category_id[]=" . urlencode($cid);
    $download_excel_url .= "&category_id[]=" . urlencode($cid);
}
// ---------------------------------------------------------------------
// 11) OUTPUT HTML (same structure as your original file)
// ---------------------------------------------------------------------
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Score Result</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
    <style>
        .zoom:hover {
            transform: scale(1.05);
            transition: transform 0.3s ease-in-out;
        }

        .fade-in {
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }

        .fade-in.visible {
            opacity: 1;
        }

        .header-section {
            background-color: #f9f9f9;
            padding: 20px 0;
            width: 100%;
            text-align: center;
        }

        .main-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 24px;
            margin-bottom: 20px;
        }

        .strokes-selector {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .score-options {
            display: none;
            position: absolute;
            background-color: white;
            border: 1px solid #ccc;
            z-index: 1000;
            width: 100%;
            max-width: 300px;
            margin-top: 5px;
            padding: 10px;
            text-align: center;
        }

        .score-options td {
            border: 1px solid #ddd;
            padding: 10px;
            cursor: pointer;
            text-align: center;
            font-size: 16px;
        }

        .score-options td:hover {
            background-color: #f1f1f1;
        }

        .selected-strokes {
            cursor: pointer;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
            text-align: center;
            font-size: 16px;
        }

        .par-input {
            text-align: center;
            background-color: #f0f0f0;
            border: none;
            font-weight: bold;
        }

        .par-input:read-only {
            background-color: #f0f0f0;
        }

        /* Compact styles for table */
        .compact-table td,
        .compact-table th {
            padding: 4px;
            font-size: 12px;
        }

        /* Compact form control */
        .compact-form-control {
            height: 30px;
            font-size: 12px;
        }

        /* Compact buttons */
        .btn-compact {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* Additional styling for mobile */
        @media (max-width: 576px) {
            .modal-dialog {
                max-width: 100%;
                margin: 1rem auto;
            }

            .modal-content {
                padding: 1rem;
            }

            .table th,
            .table td {
                font-size: 10px;
                padding: 3px;
            }

            .strokes-selector {
                width: 100%;
                font-size: 12px;
            }

            .score-options td {
                padding: 8px;
                font-size: 12px;
            }

            .selected-strokes {
                padding: 5px;
                font-size: 12px;
            }

            .btn {
                font-size: 12px;
                padding: 5px 10px;
            }

            .modal-title {
                font-size: 16px;
            }

            h5,
            h6 {
                font-size: 14px;
            }

            .par-input {
                font-size: 12px;
            }

            .stroke-option,
            .btn-light.m-1 {
                width: 30px;
                height: 30px;
                font-size: 12px;
                text-align: center;
                line-height: 30px;
            }
        }

        /* Additional custom styles */
        .modal-lg {
            max-width: 95%;
        }

        .modal-body {
            max-height: 80vh;
            overflow-y: auto;
        }

        .stroke-score,
        .btn-light.m-1 {
            width: 50px;
            height: 50px;
            font-size: 16px;
            text-align: center;
            line-height: 30px;
            border-radius: 10%;
        }

        .player-info {
            cursor: pointer;
            color: blue;
            text-decoration: underline;
        }

        .readonly-select {
        background-color: #f9f9f9;
        cursor: default;
    }

        .compact-form-control {
            width: 150px; /* Adjust this value as needed */
            min-width: 150px; /* Ensure the dropdown is wide enough to show all text */
            font-size: 16px; /* Adjust font size for better readability */
            height: auto;
        }
        .logo-preview {
            display: inline-block;
            position: relative;
            margin: 5px;
            text-align: center;
        }

        .logo-preview img {
            max-width: 80px;
            height: auto;
            border: 1px solid #ccc;
            padding: 5px;
            background: #fff;
        }

        .logo-delete {
            position: absolute;
            top: -8px;
            right: -8px;
            background: red;
            color: white;
            border-radius: 50%;
            padding: 2px 5px;
            cursor: pointer;
            font-size: 10px;
        }

        /* Player column styling */
        .player-column {
            text-align: left !important;
            min-width: 200px;
        }

        /* Professional Actions dropdown styling */
        .dropdown-menu {
            min-width: 220px;
            border: none !important;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
        }

        .dropdown-item {
            padding: 10px 20px;
            transition: all 0.2s ease;
            border-radius: 4px;
            margin: 2px 8px;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(2px);
        }

        .dropdown-header {
            font-weight: 600;
            letter-spacing: 0.5px;
            padding: 8px 20px 4px 20px;
        }

        .dropdown-divider {
            margin: 8px 16px;
            border-color: #e9ecef;
        }

        /* Professional button styling */
        .btn-dark.dropdown-toggle {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-dark.dropdown-toggle:hover {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        /* Enhanced search styling */
        .dataTables_filter {
            margin-bottom: 20px;
        }

        .dataTables_filter input {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 14px;
            width: 300px;
            transition: all 0.3s ease;
        }

        .dataTables_filter input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            outline: none;
        }

        .dataTables_filter label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 5px;
        }

        .search-message {
            max-width: 400px;
            font-size: 14px;
        }
    </style>
    <script type="text/javascript">
    const holesData = <?php echo $holes_json; ?>;
    const numHoles = <?php echo $num_holes; ?>;
</script>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <?php include("admin_header.php");?>
    <!-- Main Content -->
    <div class="header-section fade-in">
        <div class="container text-center">
            <h1 class="main-title">Result: <?php echo htmlspecialchars($event_details['event_name']); ?></h1>
            <p class="subtitle">
                <?php
                  $formatted_start_date = (new DateTime($event_details['event_start_date']))->format('d-m-Y');
                  $formatted_end_date = (new DateTime($event_details['event_end_date']))->format('d-m-Y');
                  ?>
                <?php echo htmlspecialchars($formatted_start_date) . " - " . htmlspecialchars($formatted_end_date); ?><br>
                <?php echo htmlspecialchars($event_details['event_venue']); ?>
            </p>
            
            <!-- Professional Actions Dropdown -->
            <div class="dropdown d-inline-block">
                <button class="btn btn-dark dropdown-toggle px-4 py-2" type="button" id="actionsDropdown" data-bs-toggle="dropdown" aria-expanded="false" style="border-radius: 6px; font-weight: 500;">
                    <i class="fas fa-cog me-2"></i>Actions
                </button>
                <ul class="dropdown-menu shadow-lg border-0" aria-labelledby="actionsDropdown" style="border-radius: 8px; min-width: 220px;">
                    <li><h6 class="dropdown-header text-muted small">SCOREBOARD</h6></li>
                    <li><a class="dropdown-item py-2" href="#" data-bs-toggle="modal" data-bs-target="#categoryModal">
                        <i class="fas fa-chart-line me-2 text-primary"></i>Live Scoreboard
                    </a></li>
                    <li><a class="dropdown-item py-2" href="ocb_config.php?event_mgm_id=<?php echo $event_mgm_id; ?>">
                        <i class="fas fa-sliders-h me-2 text-secondary"></i>OCB Configuration
                    </a></li>
                    <li><hr class="dropdown-divider my-2"></li>
                    <li><h6 class="dropdown-header text-muted small">DOWNLOADS</h6></li>
                    <li><a class="dropdown-item py-2" href="#" data-bs-toggle="modal" data-bs-target="#downloadResultModal">
                        <i class="fas fa-download me-2 text-success"></i>Download Results
                    </a></li>
                    <li><a class="dropdown-item py-2" href="#" data-bs-toggle="modal" data-bs-target="#groupModal">
                        <i class="fas fa-file-excel me-2 text-info"></i>Download Combined Results
                    </a></li>
                </ul>
            </div>
           <?php
           $cat_query = "
           SELECT DISTINCT category_id
           FROM event_mgm
           WHERE event_mgm_id = ?
       ";
       $cat_stmt = $conn->prepare($cat_query);
       $cat_stmt->bind_param("s", $event_mgm_id);
       $cat_stmt->execute();
       $cat_result = $cat_stmt->get_result();

       $category_ids = [];
       while ($cat_row = $cat_result->fetch_assoc()) {
           $category_ids[] = $cat_row['category_id'];
       }

       // Construct the URL with all category_id[] parameters
       $download_url = "download_results.php?event_mgm_id=" . urlencode($event_mgm_id);
       foreach ($category_ids as $cid) {
           $download_url .= "&category_id[]=" . urlencode($cid);
       }
       ?>
        </div>

    </div>
    <div class="container mx-auto my-8 fade-in px-4">
        <!-- Category Selection -->
        <div class="mb-6">
            <label for="category" class="block text-lg font-medium mb-2">Select Category:</label>
            <div class="relative">
                <select id="category" name="category" onchange="location = this.value;"
                    class="block appearance-none w-full bg-white border border-gray-300 hover:border-gray-500 px-4 py-2 pr-8 rounded leading-tight focus:outline-none focus:shadow-outline">
                    <?php foreach ($categories as $category): ?>
                        <option value="result.php?event_mgm_id=<?php echo $event_mgm_id; ?>&category_id=<?php echo $category['category_id']; ?>"
                            <?php echo $category['category_id'] == $category_id ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['category_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M10 3L3 9h14L10 3z" />
                    </svg>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table id="scoresTable" class="display text-center compact-table">
            <thead>
    <tr>
        <th>Position</th>
        <th class="player-column">Player</th>
                    <?php if ($mode === 'NETT' || $mode === 'SYSTEM36' || $mode === 'STABLEFORD') echo '<th>HCP</th>'; ?>
                                        <?php if ($mode === 'SYSTEM36') echo '<th>HCP of Day</th>'; ?>
                                <?php if ($mode === 'STABLEFORD') echo '<th>HCP of Day</th>'; ?>
        <th>ToPar</th>
        <?php foreach ($rounds as $round): ?>
        <th>Round <?php echo htmlspecialchars($round); ?></th>
        <?php endforeach; ?>
        <th>Total</th>
                                        <?php if ($mode === 'NETT') echo '<th>Nett</th>'; ?>
                                <?php if ($mode === 'SYSTEM36') echo '<th>System36 Points</th>'; ?>
                                <?php if ($mode === 'STABLEFORD') echo '<th>Stableford Points</th>'; ?>
        <th>Action</th>
    </tr>
</thead>

<tbody>
    <?php foreach ($player_scores as $details): ?>
        <tr>
    <td>
        <?php
        if ($details['position'] === 'T1') {
            echo '<a href="#" class="t1-position-link" data-bs-toggle="modal" data-bs-target="#t1PositionModal" data-player-id="' . htmlspecialchars($details['id']) . '" data-player-name="' . htmlspecialchars($details['name']) . '">' . htmlspecialchars($details['position']) . '</a>';
        } else {
            echo htmlspecialchars($details['position']);
        }
        ?>
    </td>
    <td class="player-column"><?php
        $status = '';
        if ($details['hide'] == 1) {
            switch($details['hide_rmk']) {
                case 0: $status = '<span style="display: inline-block; padding: 2px 6px; font-size: 12px; border-radius: 3px; background-color: #ff6b6b; color: white; margin-left: 5px;">WD</span>'; break;
                case 1: $status = '<span style="display: inline-block; padding: 2px 6px; font-size: 12px; border-radius: 3px; background-color: #ff6b6b; color: white; margin-left: 5px;">DQ</span>'; break;
                case 2: $status = '<span style="display: inline-block; padding: 2px 6px; font-size: 12px; border-radius: 3px; background-color: #ff6b6b; color: white; margin-left: 5px;">DNF</span>'; break;
            }
        }
        echo htmlspecialchars($details['name']) . $status . '<br><span class="text-gray-400"> ' . htmlspecialchars($details['country']) . '</span>';
    ?></td>
              <?php if ($mode === 'NETT' || $mode === 'SYSTEM36' || $mode === 'STABLEFORD') echo '<td>' . (isset($details['handicap']) ? htmlspecialchars($details['handicap']) : '-') . '</td>'; ?>
    <?php if ($mode === 'SYSTEM36') echo '<td>' . htmlspecialchars($details['hcp_of_day']) . '</td>'; ?>
    <?php if ($mode === 'STABLEFORD') echo '<td>' . htmlspecialchars($details['hcp_of_day']) . '</td>'; ?>
    <td><?php echo htmlspecialchars($details['score']); ?></td>
    <?php foreach ($rounds as $round): ?>
        <td><?php echo isset($details['rounds'][$round]) && $details['rounds'][$round] > 0 ? htmlspecialchars($details['rounds'][$round]) : '-'; ?></td>
    <?php endforeach; ?>
    <td><?php echo htmlspecialchars($details['total_strokes']); ?></td>
    <?php if ($mode === 'NETT'): ?>
            <td><?php echo htmlspecialchars($details['nett']); ?></td>
        <?php endif; ?>
    <?php if ($mode === 'SYSTEM36'): ?>
            <td><?php echo htmlspecialchars($details['system36_points']); ?></td>
        <?php endif; ?>
    <?php if ($mode === 'STABLEFORD'): ?>
            <td><?php echo htmlspecialchars($details['stableford_points']); ?></td>
        <?php endif; ?>
    <td>
        <?php
            $max_round = !empty($details['rounds']) ? max(array_keys($details['rounds'])) : 1;
        ?>
        <button class="btn btn-sm btn-primary mt-2 modify-scorecard-btn" data-bs-toggle="modal"
            data-bs-target="#addScorecardModal"
            data-player="<?php echo htmlspecialchars($details['name']); ?>"
            data-player-id="<?php echo htmlspecialchars($details['id']); ?>"
            data-round="<?php echo $max_round; ?>">
            Modify Scorecard
        </button>
    </td>
</tr>
    <?php endforeach; ?>
</tbody>
            </table>
        </div>
    </div>
    <div class="modal fade" id="downloadResultModal" tabindex="-1" aria-labelledby="downloadResultModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-sm">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="downloadResultModalLabel">Download Results</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body text-center">
            <p>Please select a format to download:</p>
            <!--<button type="button" class="btn btn-danger w-100 mb-3" onclick="window.location.href='<?php echo $download_pdf_url; ?>'">
              PDF Format
            </button>-->
            <button type="button" class="btn btn-success w-100" onclick="window.location.href='<?php echo $download_excel_url; ?>'">
              Excel Format
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- Add Scorecard Modal -->
    <div class="modal fade" id="addScorecardModal" tabindex="-1" aria-labelledby="addScorecardModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addScorecardModalLabel">Add Scorecard for <span
                            id="scorecardPlayerName"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="scorecardForm">
                        <div class="row mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <button type="button" class="btn btn-secondary btn-compact" id="prevRound"><i
                                        class="fas fa-chevron-left"></i></button>
                                <h5 id="currentRound">Round 1</h5>
                                <button type="button" class="btn btn-secondary btn-compact" id="nextRound"><i
                                        class="fas fa-chevron-right"></i></button>
                            </div>
                        </div>
                        <br>
                        <div class="mb-3" style="display: none;">
                            <label for="holesSelect">Select Number of Holes</label>
                            <select class="form-control compact-form-control readonly-select" id="holesSelect" style="pointer-events: none; width: 100%;">
                                <?php for ($i = 1; $i <= $num_holes; $i++): ?>
                                <option value="<?php echo $i; ?>"><?php echo $i; ?> Hole<?php echo $i > 1 ? 's' : ''; ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div id="scorecardTable" class="table-responsive">
                            <!-- Dynamic hole tables will be inserted here -->
                        </div>
                        <div class="text-center mt-3">
                            <h6><b>Total Score: <span id="totalScoreDisplay">0</span></b></h6>
                        </div>
                        <div class="d-grid gap-2 mt-3">
                            <button type="submit" class="btn btn-primary btn-compact">Save Scorecard</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- View Scorecard Modal -->
    <div class="modal fade" id="viewScorecardModal" tabindex="-1" aria-labelledby="viewScorecardModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewScorecardModalLabel">Scorecard for <span
                            id="viewScorecardPlayerName"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="viewScorecardTable" class="table-responsive compact-table">
                        <!-- Dynamic view hole tables will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Select Strokes Modal -->
    <div class="modal fade" id="selectStrokesModal" tabindex="-1" aria-labelledby="selectStrokesModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="selectStrokesModalLabel">Select Strokes</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div id="strokesSelection">
                        <!-- Dynamic stroke buttons will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg"> <!-- Make it larger if needed -->
    <div class="modal-content">
      <form id="categoryForm">
        <div class="modal-header">
          <h5 class="modal-title" id="categoryModalLabel">Select Categories & Manage Logos</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Category Selection -->
          <p>Please select the categories you want to view on the scoreboard:</p>
          <?php foreach ($categories as $cat): ?>
            <div class="form-check">
              <input class="form-check-input" type="checkbox" name="category_id[]" 
                     value="<?php echo $cat['category_id']; ?>" id="cat_<?php echo $cat['category_id']; ?>">
              <label class="form-check-label" for="cat_<?php echo $cat['category_id']; ?>">
                <?php echo htmlspecialchars($cat['category_name']); ?>
              </label>
            </div>
          <?php endforeach; ?>

          <hr class="my-4">
          <!-- Logo Management Section -->
          <h5>Event Logos:</h5>
          <div id="logoPreviewArea">
            <?php foreach ($logos as $lg): ?>
              <div class="logo-preview" data-logo-id="<?php echo $lg['id']; ?>">
                <img src="../img/event_logo/<?php echo htmlspecialchars($lg['logo']); ?>" alt="Logo">
                <span class="logo-delete" data-id="<?php echo $lg['id']; ?>">&times;</span>
              </div>
            <?php endforeach; ?>
          </div>
          <div class="mt-2">
            <input type="file" id="logoFileInput">
          </div>
          <p class="text-sm text-gray-500">Add new logos here. They will appear instantly on the scoreboard.</p>

          <hr class="my-4">
          <!-- Sponsor Logo Management Section -->
          <h5>Event Sponsor Logos:</h5>
          <div id="sponsorLogoPreviewArea">
            <?php foreach ($sponsor_logos as $sl): ?>
              <div class="logo-preview" data-sponsor-logo-id="<?php echo $sl['id']; ?>">
                <img src="../img/event_logo/<?php echo htmlspecialchars($sl['logo']); ?>" alt="Sponsor Logo">
                <span class="sponsor-logo-delete" data-id="<?php echo $sl['id']; ?>">&times;</span>
              </div>
            <?php endforeach; ?>
          </div>
          <div class="mt-2">
            <input type="file" id="sponsorLogoFileInput">
          </div>
          <p class="text-sm text-gray-500">Add new sponsor logos here. They will appear on the result details page.</p>
        </div>
        <div class="modal-body">
        <div class="mb-3">
      <label for="sizeInput" class="form-label">Text Size</label>
      <input
        type="number"
        class="form-control"
        id="sizeInput"
        name="size"
        value="<?php echo isset($_GET['size']) ? (int)$_GET['size'] : 16; ?>"
        placeholder="Default: 16"
      />
    </div>
    <div class="mb-3">
      <label for="playersInput" class="form-label">Players Per Page</label>
      <input
        type="number"
        class="form-control"
        id="playersInput"
        name="players"
        value="<?php echo isset($_GET['players']) ? (int)$_GET['players'] : 10; ?>"
        placeholder="Default: 10"
      />
    </div>
    <div class="mb-3">
      <label for="timeinterval" class="form-label">Page Refresh Duration (Second)</label>
      <input
        type="number"
        class="form-control"
        id="timeinterval"
        name="players"
        value="<?php echo isset($_GET['interval']) ? (int)$_GET['interval'] : 5; ?>"
        placeholder="Default: 5"
      />
    </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" id="submitCategoriesBtn">Submit</button>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="modal fade" id="groupModal" tabindex="-1" aria-labelledby="groupModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <form id="groupForm" method="post" action="excel_group_results_excel.php" target="_blank">
        <input type="hidden" name="event_mgm_id" value="<?php echo htmlspecialchars($event_mgm_id); ?>">

        <div class="modal-header">
          <h5 class="modal-title" id="groupModalLabel">Create Groups</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
          <p>You can create multiple groups. Each group can contain one or more categories, etc...</p>
          <div id="groupsContainer"></div>
          <button type="button" class="btn btn-sm btn-secondary mt-3" id="addGroupBtn">Add Group</button>

          <!-- New block: Choose GROSS vs NETT vs SYSTEM36 vs STABLEFORD -->
          <div class="mt-4">
            <label class="form-label">Select Format:</label>
            <div class="form-check form-check-inline">
              <input
                class="form-check-input"
                type="radio"
                name="override_mode"
                id="radioGross"
                value="GROSS"
                checked
              >
              <label class="form-check-label" for="radioGross">Gross</label>
            </div>
            <div class="form-check form-check-inline">
              <input
                class="form-check-input"
                type="radio"
                name="override_mode"
                id="radioNett"
                value="NETT"
              >
              <label class="form-check-label" for="radioNett">Nett</label>
            </div>
            <div class="form-check form-check-inline">
              <input
                class="form-check-input"
                type="radio"
                name="override_mode"
                id="radioSystem36"
                value="SYSTEM36"
              >
              <label class="form-check-label" for="radioSystem36">System 36</label>
            </div>
            <div class="form-check form-check-inline">
              <input
                class="form-check-input"
                type="radio"
                name="override_mode"
                id="radioStableford"
                value="STABLEFORD"
              >
              <label class="form-check-label" for="radioStableford">Stableford</label>
            </div>
            <p>Please confirmed that you have configure the <a href='event_list.php'>score configuration</a> for this event, before generating the Nett, System 36, or Stableford format</p>
          </div>
        </div>

        <div class="modal-footer">
          <!-- If you have PDF logic, you can replicate similarly -->
          <!-- e.g. <button type="button" class="btn btn-danger">Download PDF</button> -->

          <button
            type="button"
            class="btn btn-success"
            onclick="document.getElementById('groupForm').action='export_group_results_excel.php'; document.getElementById('groupForm').submit();"
          >
            Download Excel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Add T1 Position Modal -->
<div class="modal fade" id="t1PositionModal" tabindex="-1" aria-labelledby="t1PositionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="t1PositionModalLabel">T1 Sudden Death Scores</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="t1PositionContent">
                    <div class="player-info-section mb-4">
                        <h4 id="t1PlayerName"></h4>
                        <p id="t1PlayerDetails"></p>
                    </div>
                    
                    <div class="hole-data-section">
                        <h5>Hole-by-Hole Data</h5>
                        
                        <div class="existing-scores-section mb-4">
                            <h6>Existing Sudden Death Scores</h6>
                            <div id="existingScoresTable">
                                <!-- Existing scores will be loaded here -->
                                <p>Loading scores...</p>
                            </div>
                        </div>
                        
                        <div class="add-score-section">
                            <h6>Add New Score</h6>
                            <form id="addHoleScoreForm" class="row g-3">
                                <input type="hidden" id="t1PlayerId" name="player_id">
                                <input type="hidden" id="t1EventId" name="event_id" value="<?php echo $event_id; ?>">
                                <input type="hidden" id="t1CategoryId" name="category_id" value="<?php echo $category_id; ?>">
                                
                                <div class="col-md-4">
                                    <label for="holeSelect" class="form-label">Select Hole</label>
                                    <select id="holeSelect" name="custom_hole_id" class="form-select">
                                        <option value="">-- Select Hole --</option>
                                        <!-- Hole options will be loaded dynamically -->
                                    </select>
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="holePar" class="form-label">Par</label>
                                    <input type="text" class="form-control" id="holePar" readonly>
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="holeScore" class="form-label">Score</label>
                                    <input type="number" class="form-control" id="holeScore" name="score" min="1">
                                </div>
                                
                                <div class="col-12 mt-3">
                                    <button type="button" id="addScoreBtn" class="btn btn-primary">Add Score</button>
                                </div>
                            </form>
                            
                            <div id="newScoresContainer" class="mt-4">
                                <h6>New Scores to Submit</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm" id="newScoresTable">
                                        <thead>
                                            <tr>
                                                <th>Hole</th>
                                                <th>Par</th>
                                                <th>Score</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- New scores will be added here -->
                                        </tbody>
                                    </table>
                                </div>
                                <button type="button" id="submitScoresBtn" class="btn btn-success mt-2" style="display: none;">Submit All Scores</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

    <?php include("admin_footer.php");?>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
$(document).ready(function() {
    var groupCount = 0;
    var categories = <?php echo json_encode($categories); ?>;

    function createGroupElement(index) {
        // Instead of a <select>, we use checkboxes
        var categoryCheckboxes = categories.map(cat => {
            return `
              <div class="form-check">
                <input class="form-check-input" type="checkbox" 
                       name="groups[${index}][category_id][]" 
                       value="${cat.category_id}" 
                       id="g${index}cat${cat.category_id}">
                <label class="form-check-label" for="g${index}cat${cat.category_id}">
                  ${cat.category_name}
                </label>
              </div>
            `;
        }).join('');

        var html = `
          <div class="group-block border p-3 mb-3" data-group-index="${index}">
            <h6>Group ${index+1}</h6>
            <button type="button" class="btn btn-sm btn-danger mb-2 removeGroupBtn">Remove Group</button>
            <div>
              <label>Select Categories for Group ${index+1}:</label>
              ${categoryCheckboxes}
            </div>
          </div>
        `;
        return html;
    }

    $('#addGroupBtn').click(function() {
        var groupHtml = createGroupElement(groupCount);
        $('#groupsContainer').append(groupHtml);
        groupCount++;
    });

    $('#groupsContainer').on('click', '.removeGroupBtn', function() {
        $(this).closest('.group-block').remove();
    });

    // Handle T1 position link click
    $('.t1-position-link').on('click', function(e) {
        e.preventDefault();
        const playerId = $(this).data('player-id');
        const playerName = $(this).data('player-name');
        
        // Update modal title and player info
        $('#t1PositionModalLabel').text('T1 Position Sudden Death Scores');
        $('#t1PlayerName').text(playerName);
        $('#t1PlayerId').val(playerId);
        
        // Load custom holes for the current category
        loadCustomHoles();
        
        // Load existing scores for this player
        loadExistingScores(playerId);
        
        // Show the modal
        $('#t1PositionModal').modal('show');
    });
    
    // Load custom holes for the current category
    function loadCustomHoles() {
        const categoryId = $('#t1CategoryId').val();
        
        $.ajax({
            url: 'fetch_custom_holes.php',
            type: 'GET',
            data: {
                category_id: categoryId
            },
            dataType: 'json',
            success: function(data) {
                if (data.error) {
                    console.error(data.error);
                    return;
                }
                
                // Populate the hole select dropdown
                const holeSelect = $('#holeSelect');
                holeSelect.empty();
                holeSelect.append('<option value="">-- Select Hole --</option>');
                
                data.forEach(function(hole) {
                    holeSelect.append(`<option value="${hole.custom_hole_id}" data-par="${hole.par}" data-hole-number="${hole.hole_number}">Hole ${hole.hole_number}</option>`);
                });
            },
            error: function(xhr, status, error) {
                console.error("Error loading custom holes: " + error);
            }
        });
    }
    
    // Load existing sudden death scores for this player
    function loadExistingScores(playerId) {
        const eventId = $('#t1EventId').val();
        
        $.ajax({
            url: 'fetch_sudden_death_scores.php',
            type: 'GET',
            data: {
                player_id: playerId,
                event_id: eventId
            },
            dataType: 'json',
            success: function(data) {
                if (data.error) {
                    console.error(data.error);
                    $('#existingScoresTable').html('<p>Error loading scores: ' + data.error + '</p>');
                    return;
                }
                
                if (!data.length) {
                    $('#existingScoresTable').html('<p>No existing sudden death scores found.</p>');
                    return;
                }
                
                // Build the HTML table for existing scores
                let tableHtml = `
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Hole</th>
                                <th>Par</th>
                                <th>Score</th>
                                <th>To Par</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                data.forEach(function(score) {
                    const toPar = score.strokes - score.par;
                    let toParDisplay = toPar === 0 ? 'E' : (toPar > 0 ? '+' + toPar : toPar);
                    
                    tableHtml += `
                        <tr>
                            <td>Hole ${score.hole_number}</td>
                            <td>${score.par}</td>
                            <td>${score.strokes}</td>
                            <td>${toParDisplay}</td>
                            <td>
                                <button type="button" class="btn btn-danger btn-sm delete-score" 
                                    data-player-id="${playerId}"
                                    data-event-id="${eventId}"
                                    data-hole-number="${score.hole_number}">
                                    Delete
                                </button>
                            </td>
                        </tr>
                    `;
                });
                
                tableHtml += `
                        </tbody>
                    </table>
                `;
                
                $('#existingScoresTable').html(tableHtml);

                // Add click handler for delete buttons
                $('.delete-score').on('click', function() {
                    if (confirm('Are you sure you want to delete this score?')) {
                        const playerIdToDelete = $(this).data('player-id');
                        const eventIdToDelete = $(this).data('event-id');
                        const holeNumber = $(this).data('hole-number');
                        
                        $.ajax({
                            url: 'delete_sudden_death_score.php',
                            type: 'POST',
                            data: {
                                player_id: playerIdToDelete,
                                event_id: eventIdToDelete,
                                hole_number: holeNumber
                            },
                            dataType: 'json',
                            success: function(response) {
                                if (response.success) {
                                    // Reload the scores table
                                    loadExistingScores(playerIdToDelete);
                                } else {
                                    alert('Error deleting score: ' + response.message);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('Error:', error);
                                alert('Error deleting score. Please try again.');
                            }
                        });
                    }
                });
            },
            error: function(xhr, status, error) {
                console.error("Error loading existing scores: " + error);
                $('#existingScoresTable').html('<p>Error loading scores. Please try again.</p>');
            }
        });
    }
    
    // Update par when a hole is selected
    $('#holeSelect').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const par = selectedOption.data('par');
        
        $('#holePar').val(par || '');
    });
    
    // Store new scores to be submitted
    const newScores = [];
    
    // Add a new score to the list
    $('#addScoreBtn').on('click', function() {
        const holeSelect = $('#holeSelect');
        const selectedOption = holeSelect.find('option:selected');
        const customHoleId = holeSelect.val();
        const holeNumber = selectedOption.data('hole-number');
        const par = $('#holePar').val();
        const score = $('#holeScore').val();
        
        if (!customHoleId || !score) {
            alert('Please select a hole and enter a score.');
            return;
        }
        
        // Add to the newScores array
        newScores.push({
            custom_hole_id: customHoleId,
            hole_number: holeNumber,
            par: par,
            score: score
        });
        
        // Update the table
        updateNewScoresTable();
        
        // Reset the form
        holeSelect.val('');
        $('#holePar').val('');
        $('#holeScore').val('');
        
        // Show the submit button if we have scores
        if (newScores.length > 0) {
            $('#submitScoresBtn').show();
        }
    });
    
    // Update the new scores table
    function updateNewScoresTable() {
        const tbody = $('#newScoresTable tbody');
        tbody.empty();
        
        newScores.forEach(function(score, index) {
            tbody.append(`
                <tr>
                    <td>Hole ${score.hole_number}</td>
                    <td>${score.par}</td>
                    <td>${score.score}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-score" data-index="${index}">
                            Remove
                        </button>
                    </td>
                </tr>
            `);
        });
    }
    
    // Remove a score from the list
    $('#newScoresTable').on('click', '.remove-score', function() {
        const index = $(this).data('index');
        newScores.splice(index, 1);
        updateNewScoresTable();
        
        // Hide the submit button if no scores
        if (newScores.length === 0) {
            $('#submitScoresBtn').hide();
        }
    });
    
    // Submit all new scores
    $('#submitScoresBtn').on('click', function() {
        const playerId = $('#t1PlayerId').val();
        const eventId = $('#t1EventId').val();
        
        if (newScores.length === 0) {
            alert('Please add at least one score before submitting.');
            return;
        }
        
        // Prepare data for submission
        const scoreData = newScores.map(score => ({
            form_id: playerId,
            event_id: eventId,
            custom_hole_id: score.custom_hole_id,
            strokes: parseInt(score.score)
        }));

        // Submit scores via AJAX
        $.ajax({
            url: 'insert_sudden_death_scores.php',
            method: 'POST',
            data: {
                scores: JSON.stringify(scoreData)
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert('Scores submitted successfully!');
                    // Clear the form and update display
                    newScores = [];
                    updateNewScoresTable();
                    $('#submitScoresBtn').hide();
                    // Reload existing scores
                    loadExistingScores(playerId, eventId);
                } else {
                    console.error('Server error:', response);
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', {xhr, status, error});
                alert('Error submitting scores. Please try again. Details in console.');
            }
        });
    });
    
    // Reset the form when the modal is closed
    $('#t1PositionModal').on('hidden.bs.modal', function() {
        newScores.length = 0;
        updateNewScoresTable();
        $('#submitScoresBtn').hide();
        $('#holeSelect').val('');
        $('#holePar').val('');
        $('#holeScore').val('');
    });
});
</script>

    <script>
        $(document).ready(function() {
    // When hide checkbox is clicked
    $('.hide-checkbox').on('change', function() {
            var form_id = $(this).data('form-id');
            var hide    = $(this).is(':checked') ? 1 : 0;
            $.ajax({
                url: 'update_hide.php',
                type: 'POST',
                dataType: 'json',
                data: { form_id: form_id, hide: hide },
                success: function(resp) {
                    if (resp.success) {
                        console.log("Hide updated:", resp.success);
                    } else if (resp.error) {
                        alert("Error: " + resp.error);
                    }
                },
                error: function(xhr, st, er) {
                    alert("Error updating hide status.");
                }
            });
        });
});
</script>
    <script>
    $(document).ready(function() {
        $('.update-ocb-btn').on('click', function(){
            var $parent  = $(this).closest('.input-group');
            var $input   = $parent.find('.ocb-input');
            var form_id  = $input.data('form-id');
            var event_id = $input.data('event-id');
            var round    = $input.data('round');
            var ocb      = parseInt($input.val(), 10) || 0;
            if (ocb < 0) {
                alert("OCB cannot be negative.");
                return;
            }
            $.ajax({
                url: 'update_ocb.php',
                type: 'POST',
                dataType: 'json',
                data: { form_id: form_id, event_id: event_id, round: round, ocb: ocb },
                success: function(r){
                    if(r.success) {
                        alert("OCB updated successfully. Reloading...");
                        window.location.reload();
                    } else if(r.error) {
                        alert("Error: " + r.error);
                    }
                },
                error: function(){
                    alert("Error updating OCB.");
                }
            });
        });
});



</script>
    <script>
$(document).ready(function() {
    // Handle category selection submit
    $('#submitCategoriesBtn').click(function() {
        var selectedCategories = [];
        $('input[name="category_id[]"]:checked').each(function() {
            selectedCategories.push($(this).val());
        });
        var size = $('#sizeInput').val() || 16; // Default to 16 if not set
        var players = $('#playersInput').val() || 10; // Default to 10 if not set
        var interval = $('#timeinterval').val() || 5; // Default to 5 if not set
        var eventMgmId = "<?php echo $event_mgm_id; ?>";
        var baseUrl = "../scoreboard.php?event_mgm_id=" + encodeURIComponent(eventMgmId);
        baseUrl += "&size=" + encodeURIComponent(size) + "&players=" + encodeURIComponent(players) + "&interval=" + encodeURIComponent(interval);
        selectedCategories.forEach(function(catId) {
            baseUrl += "&category_id[]=" + encodeURIComponent(catId);
        });
        window.location.href = baseUrl;
    });

    // Logo upload instantly
    $('#logoFileInput').on('change', function() {
        var fileData = $(this).prop('files')[0];
        if (!fileData) return;
        var formData = new FormData();
        formData.append('event_mgm_id', "<?php echo $event_mgm_id; ?>");
        formData.append('logo', fileData);

        $.ajax({
            url: 'upload_logo.php',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function(response) {
                if (response.error) {
                    alert(response.error);
                } else {
                    // Add the new logo preview
                    $('#logoPreviewArea').append(
                        `<div class="logo-preview" data-logo-id="${response.id}">
                            <img src="../img/event_logo/${response.filename}" alt="Logo">
                            <span class="logo-delete" data-id="${response.id}">&times;</span>
                        </div>`
                    );
                    $('#logoFileInput').val('');
                }
            },
            error: function(xhr) {
                alert("Error uploading logo");
            }
        });
    });

    // Delete logo instantly
    $('#logoPreviewArea').on('click', '.logo-delete', function() {
        var logoId = $(this).data('id');
        var $parent = $(this).closest('.logo-preview');
        $.ajax({
            url: 'delete_logo.php',
            type: 'POST',
            data: { id: logoId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $parent.remove();
                } else {
                    alert(response.error || "Failed to delete logo");
                }
            },
            error: function() {
                alert("Error deleting logo");
            }
        });
    });

    // Sponsor logo upload instantly
    $('#sponsorLogoFileInput').on('change', function() {
        var fileData = $(this).prop('files')[0];
        if (!fileData) return;
        var formData = new FormData();
        formData.append('event_mgm_id', "<?php echo $event_mgm_id; ?>");
        formData.append('logo', fileData);

        $.ajax({
            url: 'upload_sponsor_logo.php',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            dataType: 'json',
            success: function(response) {
                if (response.error) {
                    alert(response.error);
                } else {
                    // Add the new sponsor logo preview
                    $('#sponsorLogoPreviewArea').append(
                        `<div class="logo-preview" data-sponsor-logo-id="${response.id}">
                            <img src="../img/event_logo/${response.filename}" alt="Sponsor Logo">
                            <span class="sponsor-logo-delete" data-id="${response.id}">&times;</span>
                        </div>`
                    );
                    $('#sponsorLogoFileInput').val('');
                }
            },
            error: function(xhr) {
                alert("Error uploading sponsor logo");
            }
        });
    });

    // Delete sponsor logo instantly
    $('#sponsorLogoPreviewArea').on('click', '.sponsor-logo-delete', function() {
        var logoId = $(this).data('id');
        var $parent = $(this).closest('.logo-preview');
        $.ajax({
            url: 'delete_sponsor_logo.php',
            type: 'POST',
            data: { id: logoId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $parent.remove();
                } else {
                    alert(response.error || "Failed to delete sponsor logo");
                }
            },
            error: function() {
                alert("Error deleting sponsor logo");
            }
        });
    });
});
</script>

    <script>
  $(document).ready(function(){
        // If you want, show a note about the mode:
        console.log("Current mode is <?php echo $mode; ?>, max_score=<?php echo $max_score; ?>");

        // Initialize DataTable with custom search
        var table = $('#scoresTable').DataTable({
            paging: false,
            info: false,
            "columnDefs": [{ "type": "num", "targets": 0 }],
            "dom": '<"top"f>rt<"bottom"><"clear">'
        });

        // Custom cross-category search functionality
        $('.dataTables_filter input').attr('placeholder', 'Search player across all categories...');

        // Override the default search to include cross-category functionality
        $('.dataTables_filter input').off('keyup search input').on('keyup search input', function() {
            var searchTerm = this.value.toLowerCase().trim();

            if (searchTerm.length >= 2) {
                // First search in current table
                table.search(searchTerm).draw();

                // If no results found in current category, search across all categories
                if (table.rows({search: 'applied'}).count() === 0 && searchTerm.length >= 3) {
                    searchAcrossCategories(searchTerm);
                }
            } else {
                table.search('').draw();
            }
        });

        // Function to search across all categories
        function searchAcrossCategories(searchTerm) {
            var eventMgmId = '<?php echo $event_mgm_id; ?>';
            var categories = <?php echo json_encode($categories); ?>;

            $.ajax({
                url: 'search_player_across_categories.php',
                type: 'POST',
                data: {
                    search_term: searchTerm,
                    event_mgm_id: eventMgmId,
                    categories: categories
                },
                dataType: 'json',
                success: function(response) {
                    if (response.found && response.category_id !== '<?php echo $category_id; ?>') {
                        // Show confirmation dialog
                        if (confirm(`Player "${response.player_name}" found in category "${response.category_name}". Navigate to that category?`)) {
                            window.location.href = `result.php?event_mgm_id=${eventMgmId}&category_id=${response.category_id}`;
                        }
                    } else if (!response.found) {
                        // Show no results message
                        showSearchMessage('No player found with that name across all categories.');
                    }
                },
                error: function() {
                    console.error('Error searching across categories');
                }
            });
        }

        // Function to show search messages
        function showSearchMessage(message) {
            // Remove existing message
            $('.search-message').remove();

            // Add message below search box
            $('.dataTables_filter').after(`
                <div class="search-message alert alert-info alert-dismissible fade show mt-2" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);

            // Auto-hide after 5 seconds
            setTimeout(function() {
                $('.search-message').fadeOut();
            }, 5000);
        }

    let currentRound = 1; // Track the current round
    let selectedHole; // Track the selected hole for stroke selection
    let scorecards = {}; // Object to store scorecards for each player

    // Update the display for the current round
    function updateRoundDisplay() {
        $('#currentRound').text(`Round ${currentRound}`);
        loadScorecardForRound(currentRound);
    }

    // Load scorecard for the current round
    function loadScorecardForRound(round) {
    let playerId = $('#addScorecardModal').data('player-id');

    // Fetch the scorecard data from the server
    $.ajax({
        url: 'fetch_scorecard.php',
        type: 'POST',
        data: {
            form_id: playerId,
            round: round,
            event_id: "<?php echo $event_id; ?>"
        },
        dataType: 'json',
        success: function (data) {
            if (data.error) {
                console.error(data.error);
            } else {
                // Store the data in the scorecards object
                scorecards[round] = data;

                // Populate the modal with fetched scores
                $('.selected-strokes').each(function () {
                    let hole = $(this).attr('id').replace('selected-strokes-', '');
                    $(this).text(scorecards[round][hole] || 'Blank');
                });

                calculateTotals();
            }
        },
        error: function (xhr, status, error) {
            console.error("An error occurred while fetching scorecard data: " + error);
        }
    });
}

    // Show score options modal
    function showSelectStrokesModal(hole) {
        selectedHole = hole;
        let strokesHtml = '';
        let maxStrokes = 10; // Initial maximum strokes displayed

        // Function to render strokes buttons
        function renderStrokesButtons(max) {
            strokesHtml = '';
            for (let i = 0; i <= max; i++) {
                strokesHtml += `<button type="button" class="btn btn-light m-1 stroke-option" data-strokes="${i}">${i}</button>`;
            }
            strokesHtml += `<button type="button" class="btn btn-light m-1" id="addMoreStrokes">+</button>`;
            $('#strokesSelection').html(strokesHtml);
        }

        // Initial render
        renderStrokesButtons(maxStrokes);

        // Event listener for the "Add More" button
        $('#strokesSelection').on('click', '#addMoreStrokes', function () {
            maxStrokes += 10; // Increase the range by 10
            renderStrokesButtons(maxStrokes);
        });

        $('#selectStrokesModal').modal('show');
    }

    // Select score
    function selectScore(strokes) {
        $(`#selected-strokes-${selectedHole}`).text(strokes);
        $('#selectStrokesModal').modal('hide');
        updateScore(selectedHole, strokes); // Update the score for the selected hole
        calculateTotals(); // Update total strokes, par, and score after selection
    }

    // Update the score for a specific hole
    function updateScore(hole, strokes) {
    let par = parseInt($(`#par-${hole}`).val()) || 0;
    let score = strokes > 0 ? strokes - par : 0; // Only calculate score if strokes > 0
    $(`#total-strokes-${hole}`).text(strokes > 0 ? score : 'Blank'); // Show Blank if strokes are 0
    calculateTotals(); // Recalculate totals after updating the score
}

    // Calculate total strokes, par, and scores for "out" and "in" sections
    function calculateTotals() {
        let outStrokesTotal = 0, inStrokesTotal = 0;
        let outParTotal = 0, inParTotal = 0;
        let totalScore = 0;

        // Calculate totals for holes 1-9 (OUT)
        for (let i = 1; i <= 9; i++) {
            let strokes = parseInt($(`#selected-strokes-${i}`).text()) || 0;
            let par = parseInt($(`#par-${i}`).val()) || 0;
            let score = strokes - par;

            if (strokes > 0) {
                outStrokesTotal += strokes;
                totalScore += score;
            }
            $(`#total-strokes-${i}`).text(totalScore); // Display cumulative score
        }
        $('#out-total-strokes').text(outStrokesTotal);
        $('#total-out-scores').text(totalScore);

        // Calculate totals for holes 10-18 (IN)
        for (let i = 10; i <= 18; i++) {
            let strokes = parseInt($(`#selected-strokes-${i}`).text()) || 0;
            let par = parseInt($(`#par-${i}`).val()) || 0;
            let score = strokes - par;

            if (strokes > 0) {
                inStrokesTotal += strokes;
                totalScore += score;
            }
            $(`#total-strokes-${i}`).text(totalScore); // Display cumulative score
        }
        $('#in-total-strokes').text(inStrokesTotal);
        $('#total-in-scores').text(totalScore);

        // Update Grand Totals
        $('#total-strokes').text(outStrokesTotal + inStrokesTotal);
        $('#total-scores').text(totalScore);
        $('#totalScoreDisplay').text(totalScore);
    }

    // Initialize score options
    function initializeScoreOptions() {
        $('.selected-strokes').each(function () {
            $(this).click(function () {
                const hole = $(this).attr('id').replace('selected-strokes-', '');
                showSelectStrokesModal(hole);
            });
        });
    }

    // Generate the hole tables based on the number of holes selected
    // Generate the hole tables based on the number of holes selected
function generateHoleTables() {
    const scorecardTable = $('#scorecardTable');
    scorecardTable.empty();

    if (holesData.length === 0) {
        // If no hole data is found, display a message
        scorecardTable.append('<p>No hole data found for this event.</p>');
        return;
    }

    let tableHtml = '<table class="table table-bordered text-center compact-table">';

    // Headers for holes 1-9 and OUT
    tableHtml += '<thead><tr><th>Hole</th>';
    for (let i = 1; i <= Math.min(numHoles, 9); i++) {
        tableHtml += `<th>${i}</th>`;
    }
    if (numHoles > 9) {
        tableHtml += '<th>OUT</th>';
        for (let i = 10; i <= numHoles; i++) {
            tableHtml += `<th>${i}</th>`;
        }
        tableHtml += '<th>IN</th>';
        tableHtml += '<th>Total</th>'; // Add Total column here
    } else {
        tableHtml += '<th>OUT</th>'; // Only add OUT column for less than 10 holes
    }
    tableHtml += '</tr></thead><tbody>';

    // Par row for holes 1-9 and OUT
    tableHtml += '<tr><td>Par</td>';
    for (let i = 1; i <= Math.min(numHoles, 9); i++) {
        const holeData = holesData.find(hole => hole.hole_number === i);
        const par = holeData ? holeData.par : 4;  // Default to 4 if not found
        tableHtml += `<td><input type="number" class="form-control par-input" style="width:50px;" id="par-${i}" value="${par}" readonly></td>`;
    }
    if (numHoles > 9) {
        tableHtml += '<td id="out-total-par">0</td>';
        for (let i = 10; i <= numHoles; i++) {
            const holeData = holesData.find(hole => hole.hole_number === i);
            const par = holeData ? holeData.par : 4;  // Default to 4 if not found
            tableHtml += `<td><input type="number" class="form-control par-input" style="width:50px;" id="par-${i}" value="${par}" readonly></td>`;
        }
        tableHtml += '<td id="in-total-par">0</td>';
        tableHtml += '<td id="total-par">0</td>'; // Add Total par here
    } else {
        tableHtml += '<td id="out-total-par">0</td>'; // Only add OUT par for less than 10 holes
    }
    tableHtml += '</tr>';

    // Strokes row for holes 1-9 and OUT
    tableHtml += '<tr><td>Strokes</td>';
    for (let i = 1; i <= Math.min(numHoles, 9); i++) {
        tableHtml += `<td><div class="strokes-selector">
            <div class="selected-strokes" id="selected-strokes-${i}">Blank</div>
        </div></td>`;
    }
    if (numHoles > 9) {
        tableHtml += '<td id="out-total-strokes">0</td>';
        for (let i = 10; i <= numHoles; i++) {
            tableHtml += `<td><div class="strokes-selector">
                <div class="selected-strokes" id="selected-strokes-${i}">Blank</div>
            </div></td>`;
        }
        tableHtml += '<td id="in-total-strokes">0</td>';
        tableHtml += '<td id="total-strokes">0</td>'; // Add Total strokes here
    } else {
        tableHtml += '<td id="out-total-strokes">0</td>'; // Only add OUT strokes for less than 10 holes
    }
    tableHtml += '</tr>';

    // Score row for holes 1-9 and OUT
    tableHtml += '<tr><td>Score</td>';
    for (let i = 1; i <= Math.min(numHoles, 9); i++) {
        tableHtml += `<td><span id="total-strokes-${i}">0</span></td>`;
    }
    if (numHoles > 9) {
        tableHtml += '<td><span id="total-out-scores">0</span></td>';
        for (let i = 10; i <= numHoles; i++) {
            tableHtml += `<td><span id="total-strokes-${i}">0</span></td>`;
        }
        tableHtml += '<td><span id="total-in-scores">0</span></td>';
        tableHtml += '<td><span id="total-scores">0</span></td>'; // Add Total score here
    } else {
        tableHtml += '<td><span id="total-out-scores">0</span></td>'; // Only add OUT score for less than 10 holes
    }
    tableHtml += '</tr>';

    tableHtml += '</tbody></table>';
    scorecardTable.append(tableHtml);
    initializeScoreOptions();
    calculateOutAndInTotals(); // Calculate initial OUT and IN totals
}


    function calculateOutAndInTotals() {
        let outParTotal = 0, inParTotal = 0;
        let cumulativeScore = 0; // To keep track of the cumulative score from previous holes
        let outScoreTotal = 0, inScoreTotal = 0;

        // Calculate totals for holes 1-9 (OUT)
        for (let i = 1; i <= 9; i++) {
            let par = parseInt($(`#par-${i}`).val()) || 0;
            let strokes = parseInt($(`#selected-strokes-${i}`).text()) || 0;
            let score;

            if (strokes > 0) { // If strokes are entered
                score = strokes - par;
                cumulativeScore += score; // Add the score to the cumulative score
            } else { // If no strokes entered
                score = 0; // Keep the score 0 for this hole
            }

            $(`#total-strokes-${i}`).text(cumulativeScore); // Display the cumulative score
            outParTotal += par;
        }

        outScoreTotal = cumulativeScore; // Save OUT total
        $('#out-total-par').text(outParTotal);

        // Calculate totals for holes 10-18 (IN)
        for (let i = 10; i <= 18; i++) {
            let par = parseInt($(`#par-${i}`).val()) || 0;
            let strokes = parseInt($(`#selected-strokes-${i}`).text()) || 0;
            let score;

            if (strokes > 0) { // If strokes are entered
                score = strokes - par;
                cumulativeScore += score; // Add the score to the cumulative score
            } else { // If no strokes entered
                score = 0; // Keep the score 0 for this hole
            }

            $(`#total-strokes-${i}`).text(cumulativeScore); // Display the cumulative score
            inParTotal += par;
        }

        inScoreTotal = cumulativeScore - outScoreTotal; // Calculate IN total (difference between IN and OUT)
        $('#in-total-par').text(inParTotal);

        // Display overall totals
        $('#total-par').text(outParTotal + inParTotal);
        $('#totalScoreDisplay').text(cumulativeScore); // Display the final cumulative score
        $('#total-scores').text(cumulativeScore); // Ensure the total score is displayed correctly
    }

    // Handle stroke selection
    $('#strokesSelection').on('click', '.stroke-option', function () {
        const strokes = $(this).data('strokes');
        selectScore(strokes);
    });

    // Initialize scorecard modal
    $('#addScorecardModal').on('show.bs.modal', function (event) {
    const button = $(event.relatedTarget); // Button that triggered the modal
    const playerName = button.data('player'); // Extract info from data-* attributes
    const playerId = button.data('player-id');
    const modal = $(this);
    modal.find('#scorecardPlayerName').text(playerName);
    modal.data('player-id', playerId);

    // Reset form and set current round to the latest round
    modal.find('form')[0].reset();
    currentRound = button.data('round') || 1; // Set to the latest round
    scorecards[playerId] = scorecards[playerId] || {}; // Initialize scorecards for player if not exist

    // Automatically set dropdown to maximum value
    const maxHoles = $('#holesSelect option').length;
    $('#holesSelect').val(maxHoles);

    // Generate the table based on maximum holes
    generateHoleTables();
    updateRoundDisplay(); // Display the current round

    // Load the scorecard for the current round
    loadScorecardForRound(currentRound);
});

    // Save form and update the UI
    $('#scorecardForm').on('submit', function (event) {
        event.preventDefault();

        const playerName = $('#scorecardPlayerName').text();
        const playerId = $('#addScorecardModal').data('player-id');
        const round = currentRound;

        let scores = {};
        let totalScore = 0;

        $('.selected-strokes').each(function () {
            const hole = $(this).attr('id').replace('selected-strokes-', '');
            const score = parseInt($(this).text()) || 0;
            scores[hole] = score;
            totalScore += score;
        });
        
        $.ajax({
    url: 'insert_scorecard.php',
    type: 'POST',
    data: {
        form_id: playerId,
        round: round,
        event_id: "<?php echo $event_id; ?>",
        scores: JSON.stringify(scores) // Convert the scores object to a JSON string
    },
    dataType: 'json',
    success: function (response) {
        if (response.error) {
            console.error(response.error);
            alert("Error: " + response.error);
        } else {
            alert(response.success);
            $('#addScorecardModal').modal('hide');
            window.location.reload(); 
        }
    },
    error: function (xhr, status, error) {
        console.error("An error occurred: " + error);
        alert("An error occurred while saving the scorecard.");
    }
});

        console.log(`Player: ${playerName}, Round: ${round}, Scores: ${JSON.stringify(scores)}`);

        // Save the scores for the current round
        scorecards[playerId][round] = scores;

        // Update the UI with the calculated scores
        $(`#r${round}-score-${playerId}`).text(totalScore);

        // Update total score
        const r1Score = parseInt($(`#r1-score-${playerId}`).text()) || 0;
        const r2Score = parseInt($(`#r2-score-${playerId}`).text()) || 0;
        $(`#total-score-${playerId}`).text(r1Score + r2Score);

        $('#addScorecardModal').modal('hide');
    });

    // Handle next/previous round navigation
    $('#nextRound').click(function () {
    saveCurrentRoundScores();
    currentRound++;
    updateRoundDisplay();
    loadScorecardForRound(currentRound); // Load data for the new round
});
$('#prevRound').click(function () {
    if (currentRound > 1) {
        saveCurrentRoundScores();
        currentRound--;
        updateRoundDisplay();
        loadScorecardForRound(currentRound); // Load data for the new round
    }
});

    // Save current round scores
    function saveCurrentRoundScores() {
    const playerId = $('#addScorecardModal').data('player-id');
    const round = currentRound;

    let scores = {};
    $('.selected-strokes').each(function () {
        const hole = $(this).attr('id').replace('selected-strokes-', '');
        const score = parseInt($(this).text()) || 0;
        scores[hole] = score;
    });

    scorecards[round] = scores; // Save scores for the current round
}

    // Initialize the form with the correct number of holes
    $('#holesSelect').change(generateHoleTables);

    // Initial setup
    generateHoleTables();

    // Player info click event to show rounds and scores
    $('.player-info').click(function () {
        const playerId = $(this).data('player-id');
        const playerName = $(this).text();
        $('#viewScorecardPlayerName').text(playerName);
        generateViewScorecardTable(playerId);
        $('#viewScorecardModal').modal('show');
    });

    function generateViewScorecardTable(playerId) {
        const scorecardTable = $('#viewScorecardTable');
        scorecardTable.empty();

        const numHoles = parseInt($('#holesSelect').val()) || 18; // Default to 18 if not set
        let outParTotal = 0, inParTotal = 0;

        let tableHtml = '<table class="table table-bordered text-center compact-table">';
        for (let round in scorecards[playerId]) {
            // Table Headers
            tableHtml += `<thead><tr><th colspan="${numHoles + 3}">Round ${round}</th></tr>`;
            tableHtml += '<tr><th>Hole</th>';
            for (let i = 1; i <= numHoles; i++) {
                tableHtml += `<th>${i}</th>`;
            }
            if (numHoles > 9) {
                tableHtml += '<th>OUT</th>';
            }
            if (numHoles > 9) {
                tableHtml += '<th>IN</th>';
                tableHtml += '<th>Total</th>';
            } else {
                tableHtml += '<th>Total</th>';
            }
            tableHtml += '</tr></thead><tbody>';

            // Par row
            tableHtml += '<tr><td>Par</td>';
            for (let i = 1; i <= numHoles; i++) {
                const par = $(`#par-${i}`).val() || 4;
                tableHtml += `<td>${par}</td>`;
                if (i <= 9) {
                    outParTotal += parseInt(par);
                } else {
                    inParTotal += parseInt(par);
                }
            }
            if (numHoles > 9) {
                tableHtml += `<td>${outParTotal}</td>`;
                tableHtml += `<td>${inParTotal}</td>`;
                tableHtml += `<td>${outParTotal + inParTotal}</td>`;
            } else {
                tableHtml += `<td>${outParTotal}</td>`;
            }
            tableHtml += '</tr>';

            // Strokes row
            tableHtml += '<tr><td>Strokes</td>';
            let outStrokesTotal = 0, inStrokesTotal = 0;
            for (let i = 1; i <= numHoles; i++) {
                const strokes = scorecards[playerId][round][i] || 0;
                tableHtml += `<td>${strokes}</td>`;
                if (i <= 9) {
                    outStrokesTotal += parseInt(strokes);
                } else {
                    inStrokesTotal += parseInt(strokes);
                }
            }
            if (numHoles > 9) {
                tableHtml += `<td>${outStrokesTotal}</td>`;
                tableHtml += `<td>${inStrokesTotal}</td>`;
                tableHtml += `<td>${outStrokesTotal + inStrokesTotal}</td>`;
            } else {
                tableHtml += `<td>${outStrokesTotal}</td>`;
            }
            tableHtml += '</tr>';

            // Score row
            tableHtml += '<tr><td>Score</td>';
            let totalScore = 0;
            for (let i = 1; i <= numHoles; i++) {
                const par = parseInt($(`#par-${i}`).val()) || 4;
                const strokes = parseInt(scorecards[playerId][round][i]) || 0;
                const score = strokes - par;
                totalScore += score;
                tableHtml += `<td>${score}</td>`;
            }
            if (numHoles > 9) {
                tableHtml += `<td>${outStrokesTotal - outParTotal}</td>`;
                tableHtml += `<td>${inStrokesTotal - inParTotal}</td>`;
                tableHtml += `<td>${totalScore}</td>`;
            } else {
                tableHtml += `<td>${totalScore}</td>`;
            }
            tableHtml += '</tr>';
        }

        tableHtml += '</tbody></table>';
        scorecardTable.append(tableHtml);
    }

    // Function to apply CSS classes based on the score
    function getScoreClass(strokes, par) {
        if (strokes == par) return 'par';
        if (strokes < par) return 'birdie'; // Apply to birdies, eagles, etc.
        if (strokes > par) return 'bogey';  // Apply to bogey or worse
        return ''; // Default class for standard strokes
    }

    function calculateViewOutAndInTotals(numHoles) {
        let outParTotal = 0, inParTotal = 0;
        let outStrokesTotal = 0, inStrokesTotal = 0;
        let outScoreTotal = 0, inScoreTotal = 0;

        for (let i = 1; i <= 9; i++) {
            outParTotal += parseInt($(`#par-${i}`).val()) || 0;
            outStrokesTotal += parseInt($(`#selected-strokes-${i}`).text()) || 0;
            outScoreTotal += (parseInt($(`#selected-strokes-${i}`).text()) || 0) - (parseInt($(`#par-${i}`).val()) || 0);
        }
        $('#view-out-total-par').text(outParTotal);
        $('#view-out-total-strokes').text(outStrokesTotal);
        $('#view-total-out-scores').text(outScoreTotal);

        for (let i = 10; i <= numHoles; i++) {
            inParTotal += parseInt($(`#par-${i}`).val()) || 0;
            inStrokesTotal += parseInt($(`#selected-strokes-${i}`).text()) || 0;
            inScoreTotal += (parseInt($(`#selected-strokes-${i}`).text()) || 0) - (parseInt($(`#par-${i}`).val()) || 0);
        }
        $('#view-in-total-par').text(inParTotal);
        $('#view-in-total-strokes').text(inStrokesTotal);
        $('#view-total-in-scores').text(inScoreTotal);

        // Calculate Grand Totals (OUT + IN)
        $('#view-total-par').text(outParTotal + inParTotal);
        $('#view-total-strokes').text(outStrokesTotal + inStrokesTotal);
        $('#view-total-scores').text(outScoreTotal + inScoreTotal);
    }
});

// Mobile menu toggle
document.getElementById('menu-toggle').addEventListener('click', () => {
    document.getElementById('mobile-menu').classList.toggle('hidden');
});

// Fade-in animation
document.addEventListener('DOMContentLoaded', function () {
    const sections = document.querySelectorAll('.fade-in');
    const options = {
        threshold: 0.1
    };

    const observer = new IntersectionObserver(function (entries, observer) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                observer.unobserve(entry.target);
            }
        });
    }, options);

    sections.forEach(section => {
        observer.observe(section);
    });
});
    </script>
</body>

</html>
